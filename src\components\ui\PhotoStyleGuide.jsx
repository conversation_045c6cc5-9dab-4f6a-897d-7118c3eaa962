'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * PhotoStyleGuide - Component for implementing BAKASANA photo guidelines
 * Transforms from "exclusive luxury" to "warm elegance"
 * 
 * PHOTOGRAPHY TRANSFORMATION GUIDELINES:
 * - Golden hour lighting (warm, soft)
 * - Authentic moments (not perfect poses)
 * - Emotional engagement ('chcę tam być' vs 'podzi<PERSON><PERSON> z daleka')
 * - Details: hands on mat, closed eyes, sweat drops
 * - Filter: bright, warm, slightly blurred like memory
 */

// Photo filter presets for warm elegance
const photoFilters = {
  goldenHour: {
    filter: 'brightness(1.1) contrast(0.95) saturate(1.2) sepia(0.1) hue-rotate(10deg)',
    overlay: 'linear-gradient(45deg, rgba(255, 223, 186, 0.1) 0%, rgba(255, 239, 213, 0.05) 100%)'
  },
  authentic: {
    filter: 'brightness(1.05) contrast(0.9) saturate(1.1) blur(0.3px)',
    overlay: 'linear-gradient(135deg, rgba(232, 180, 184, 0.08) 0%, rgba(212, 165, 116, 0.06) 100%)'
  },
  memory: {
    filter: 'brightness(1.08) contrast(0.92) saturate(1.15) blur(0.5px) sepia(0.05)',
    overlay: 'radial-gradient(circle at center, rgba(255, 245, 235, 0.1) 0%, transparent 70%)'
  },
  warmDetail: {
    filter: 'brightness(1.12) contrast(0.88) saturate(1.25) sepia(0.08)',
    overlay: 'linear-gradient(to bottom, rgba(253, 251, 247, 0.1) 0%, rgba(249, 243, 237, 0.05) 100%)'
  }
};

// Photo composition guidelines
const compositionTypes = {
  authentic: {
    description: "Złap moment - śmiech, skupienie, ulga",
    examples: ["Spontaniczny śmiech podczas pozycji", "Zamknięte oczy w medytacji", "Ulgę po trudnej pozycji"]
  },
  details: {
    description: "Pokaż detale - dłonie, krople potu, tekstury",
    examples: ["Dłonie na macie", "Krople potu na czole", "Tekstura maty jogi", "Stopy w piasku"]
  },
  environment: {
    description: "Kontekst miejsca - ale nie dominujący",
    examples: ["Tarasy ryżowe w tle", "Plaża o świcie", "Świątynia jako tło", "Tropikalna zieleń"]
  },
  connection: {
    description: "Połączenie z naturą i sobą",
    examples: ["Dotyk ziemi", "Wiatr we włosach", "Słońce na skórze", "Oddech w naturze"]
  }
};

export function PhotoContainer({ 
  children, 
  filter = 'goldenHour', 
  className = '', 
  breathing = true,
  overlay = true,
  ...props 
}) {
  const selectedFilter = photoFilters[filter];
  
  return (
    <div
      className={cn(
        "relative overflow-hidden",
        breathing && "breathe-gentle",
        className
      )}
      style={{
        filter: selectedFilter.filter
      }}
      {...props}
    >
      {children}
      {overlay && (
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{
            background: selectedFilter.overlay,
            mixBlendMode: 'soft-light'
          }}
        />
      )}
    </div>
  );
}

export function AuthenticMoment({ 
  src, 
  alt, 
  caption, 
  moment = "authentic",
  className = '',
  ...props 
}) {
  return (
    <PhotoContainer 
      filter={moment} 
      className={cn("group cursor-pointer", className)}
      {...props}
    >
      <img 
        src={src} 
        alt={alt}
        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
        loading="lazy"
      />
      {caption && (
        <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/30 to-transparent">
          <p className="text-white text-sm font-caveat italic opacity-90">
            {caption}
          </p>
        </div>
      )}
      
      {/* Breathing glow effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 breathe-glow pointer-events-none" />
    </PhotoContainer>
  );
}

export function GoldenHourGallery({ images, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
        className
      )}
      {...props}
    >
      {images.map((image, index) => (
        <AuthenticMoment
          key={index}
          src={image.src}
          alt={image.alt}
          caption={image.caption}
          moment={image.moment || 'goldenHour'}
          className="aspect-[4/5] rounded-none"
        />
      ))}
    </div>
  );
}

export function PhotoStyleGuideDemo({ className = '', ...props }) {
  const exampleImages = [
    {
      src: "/api/placeholder/400/500",
      alt: "Spontaniczny śmiech podczas jogi",
      caption: "Złap moment radości",
      moment: "authentic"
    },
    {
      src: "/api/placeholder/400/500", 
      alt: "Dłonie na macie w złotej godzinie",
      caption: "Detale mają znaczenie",
      moment: "warmDetail"
    },
    {
      src: "/api/placeholder/400/500",
      alt: "Medytacja o wschodzie słońca",
      caption: "Autentyczne skupienie",
      moment: "memory"
    }
  ];

  return (
    <div className={cn("space-y-12", className)} {...props}>
      <div className="text-center">
        <h3 className="font-playfair text-2xl mb-6 text-charcoal">
          Przewodnik Fotograficzny: Od Perfekcji do Autentyczności
        </h3>
        <p className="body-breathing max-w-2xl mx-auto">
          Każde zdjęcie powinno wywoływać uczucie <span className="warm-underline">'chcę tam być'</span> 
          zamiast <em>'podziwiam z daleka'</em>
        </p>
      </div>

      <GoldenHourGallery images={exampleImages} />

      <div className="grid md:grid-cols-2 gap-8">
        <div className="space-y-4">
          <h4 className="font-playfair text-xl text-charcoal">Zamiast perfekcyjnych póz:</h4>
          <ul className="space-y-2 text-charcoal-light">
            {compositionTypes.authentic.examples.map((example, index) => (
              <li key={index} className="flex items-start">
                <span className="text-rose-gold mr-2">•</span>
                {example}
              </li>
            ))}
          </ul>
        </div>
        
        <div className="space-y-4">
          <h4 className="font-playfair text-xl text-charcoal">Pokaż detale:</h4>
          <ul className="space-y-2 text-charcoal-light">
            {compositionTypes.details.examples.map((example, index) => (
              <li key={index} className="flex items-start">
                <span className="text-rose-gold mr-2">•</span>
                {example}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-gradient-to-r from-dawn-cream to-dawn-peach p-8 text-center">
        <h4 className="font-playfair text-xl mb-4 text-charcoal">Filtr: Jasny, ciepły, jak wspomnienie</h4>
        <p className="text-charcoal-light">
          Wszystkie zdjęcia w <span className="warm-underline">złotej godzinie</span> - 
          ciepłe światło, które sprawia, że chce się tam być
        </p>
      </div>
    </div>
  );
}

// CSS-in-JS styles for photo effects (to be added to breathing-interactions.css)
export const photoEffectsCSS = `
/* Photo Style Guide Effects */
.photo-golden-hour {
  filter: brightness(1.1) contrast(0.95) saturate(1.2) sepia(0.1) hue-rotate(10deg);
}

.photo-authentic {
  filter: brightness(1.05) contrast(0.9) saturate(1.1) blur(0.3px);
}

.photo-memory {
  filter: brightness(1.08) contrast(0.92) saturate(1.15) blur(0.5px) sepia(0.05);
}

.photo-warm-detail {
  filter: brightness(1.12) contrast(0.88) saturate(1.25) sepia(0.08);
}

/* Photo hover effects */
.photo-container:hover {
  transform: scale(1.02);
  transition: transform 0.5s ease;
}

.photo-overlay-golden {
  background: linear-gradient(45deg, rgba(255, 223, 186, 0.1) 0%, rgba(255, 239, 213, 0.05) 100%);
  mix-blend-mode: soft-light;
}

.photo-overlay-warm {
  background: radial-gradient(circle at center, rgba(255, 245, 235, 0.1) 0%, transparent 70%);
  mix-blend-mode: soft-light;
}
`;

export default PhotoContainer;