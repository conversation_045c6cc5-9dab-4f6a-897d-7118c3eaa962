// Metadata export for Next.js
import type { Metadata } from 'next';
import Perfect<PERSON><PERSON>bar from '@/components/PerfectNavbar';
import OldMoneyHero from '@/components/Hero/OldMoneyHero';
import OldMoneyAbout from '@/components/About/OldMoneyAbout';
import OldMoneyServices from '@/components/Services/OldMoneyServices';
import OldMoneyFooter from '@/components/Footer/OldMoneyFooter';

export const metadata: Metadata = {
  title: 'BAKASANA - Retreaty Jogi Premium | Old Money Design',
  description: 'Odkryj transformacyjne retreaty jogi w duchowych sercach Azji. Premium doświadczenia na Bali i Sri Lance w stylu quiet luxury.',
  keywords: 'retreaty jogi, Bali, Sri Lanka, old money, premium yoga, quiet luxury',
};

export default function OldMoneyPage() {
  return (
    <div className="min-h-screen bg-sanctuary">
      <PerfectNavbar />
      <OldMoneyHero />
      <OldMoneyAbout />
      <OldMoneyServices />
      <OldMoneyFooter />
    </div>
  );
}