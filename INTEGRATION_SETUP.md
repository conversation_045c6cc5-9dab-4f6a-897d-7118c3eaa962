# 🚀 PRZEWODNIK KONFIGURACJI INTEGRACJI - BAKASANA

## 📋 SPIS TREŚCI
1. [Formularze - Web3Forms](#1-formularze---web3forms)
2. [Google Analytics 4](#2-google-analytics-4)
3. [Google Tag Manager](#3-google-tag-manager)
4. [<PERSON><PERSON><PERSON><PERSON> rezerwacji](#4-kalendarz-rezerwacji)
5. [Cross-browser testing](#5-cross-browser-testing)
6. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> działania](#6-weryfikacja-działania)

---

## 1. FORMULARZE - WEB3FORMS

### ✅ **Krok 1: Zał<PERSON>ż konto Web3Forms**
1. Id<PERSON> na: https://web3forms.com
2. Kliknij **"Get Started Free"**
3. Zarejestruj się emailem: `<EMAIL>`
4. Potwierdź email

### ✅ **Krok 2: Stw<PERSON>rz formularz**
1. Po zalogowaniu kliknij **"Create New Form"**
2. <PERSON><PERSON>wa formularza: `Bakasana - <PERSON><PERSON> kontaktowy`
3. <PERSON><PERSON> docel<PERSON>y: `<EMAIL>`
4. <PERSON>kopiuj **Access Key** (np. `a1b2c3d4-e5f6-7890-abcd-ef1234567890`)

### ✅ **Krok 3: Zaktualizuj zmienne środowiskowe**
W pliku `.env.local` zamień:
```bash
NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY=your-web3forms-access-key
```
Na swój prawdziwy klucz z Web3Forms.

### ✅ **Krok 4: Testowanie**
```bash
npm run dev
# Idź na http://localhost:3002/kontakt
# Wypełnij i wyślij formularz
# Sprawdź czy email dotarł na <EMAIL>
```

---

## 2. GOOGLE ANALYTICS 4

### ✅ **Krok 1: Stwórz konto Google Analytics**
1. Idź na: https://analytics.google.com
2. Zaloguj się kontem Google
3. Kliknij **"Rozpocznij pomiary"**
4. Stwórz konto: `Bakasana Travel Blog`

### ✅ **Krok 2: Dodaj właściwość**
1. Nazwa właściwości: `bakasana-travel.blog`
2. Strefa czasowa: `Polska`
3. Waluta: `Polski złoty (PLN)`
4. Kategoria branży: `Podróże`

### ✅ **Krok 3: Skopiuj Measurement ID**
1. Idź do **Admin** → **Strumienie danych**
2. Kliknij na swój strumień danych
3. Skopiuj **Measurement ID** (format: `G-XXXXXXXXXX`)

### ✅ **Krok 4: Zaktualizuj zmienne środowiskowe**
W pliku `.env.local` zamień:
```bash
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```
Na swój prawdziwy Measurement ID.

---

## 3. GOOGLE TAG MANAGER

### ✅ **Krok 1: Stwórz konto GTM**
1. Idź na: https://tagmanager.google.com
2. Kliknij **"Utwórz konto"**
3. Nazwa konta: `Bakasana`
4. Nazwa kontenera: `bakasana-travel.blog`
5. Platforma docelowa: **Web**

### ✅ **Krok 2: Skopiuj Container ID**
1. Po utworzeniu kontenera skopiuj **Container ID** (format: `GTM-XXXXXXX`)

### ✅ **Krok 3: Zaktualizuj zmienne środowiskowe**
W pliku `.env.local` dodaj:
```bash
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
```

### ✅ **Krok 4: Połącz z Google Analytics**
1. W GTM idź do **Tagi** → **Nowy**
2. Wybierz **Google Analytics: GA4 Configuration**
3. Measurement ID: Twój GA4 ID
4. Wyzwalacz: **All Pages**
5. Zapisz i opublikuj

---

## 4. KALENDARZ REZERWACJI

### ✅ **Kalendarz jest już skonfigurowany!**
- ✅ Responsywny design
- ✅ Integracja z systemem bookingu
- ✅ Analytics tracking
- ✅ Early bird pricing
- ✅ Lista oczekujących

### ✅ **Dodawanie nowych retreatów**
Edytuj plik `src/components/BookingCalendar.jsx`:
```javascript
{
  id: 6,
  title: 'Nowy Retreat - Nazwa',
  start: new Date(2025, 11, 15), // Grudzień 15, 2025
  end: new Date(2025, 11, 22),   // Grudzień 22, 2025
  price: 2500,
  spotsLeft: 15,
  totalSpots: 15,
  status: 'available',
  level: 'wszystkie poziomy',
  location: 'Lokalizacja',
  description: 'Opis retreatu...',
  includes: ['Lista', 'Tego', 'Co', 'Zawiera'],
  instructor: 'Julia Jakubowicz',
  image: '/images/retreats/nazwa-obrazka.jpg',
  earlyBirdPrice: 2200,
  earlyBirdDeadline: new Date(2025, 9, 1)
}
```

---

## 5. CROSS-BROWSER TESTING

### ✅ **Uruchom testy automatyczne**
```bash
# Uruchom serwer deweloperski
npm run dev

# W nowym terminalu uruchom testy
npm run test:cross-browser

# Lub wszystko na raz
npm run test:browsers
```

### ✅ **Sprawdź raporty**
- JSON: `reports/cross-browser-test.json`
- HTML: `reports/cross-browser-test.html`

### ✅ **Wsparcie przeglądarek**
- ✅ Chrome 88+ (99.9%)
- ✅ Firefox 85+ (99.9%)
- ✅ Safari 14+ (99.5%)
- ✅ Edge 88+ (99.9%)
- ✅ Mobile browsers (95%+)

---

## 6. WERYFIKACJA DZIAŁANIA

### ✅ **Checklist końcowy**

#### Formularze:
- [ ] Formularz kontaktowy wysyła emaile
- [ ] Walidacja działa poprawnie
- [ ] Tracking w Analytics

#### Analytics:
- [ ] Google Analytics śledzi wizyty
- [ ] GTM zbiera dane
- [ ] Custom events działają

#### Kalendarz:
- [ ] Kalendarz się ładuje
- [ ] Eventy są klikalne
- [ ] Modal z detalami działa
- [ ] Przycisk rezerwacji działa

#### Cross-browser:
- [ ] Strona działa w Chrome
- [ ] Strona działa w Firefox
- [ ] Strona działa w Safari
- [ ] Strona działa na mobile

### ✅ **Testowanie w różnych przeglądarkach**

1. **Chrome/Edge**: Otwórz http://localhost:3002
2. **Firefox**: Otwórz http://localhost:3002
3. **Safari** (macOS): Otwórz http://localhost:3002
4. **Mobile**: Użyj DevTools → Device Mode

### ✅ **Sprawdzenie Analytics**

1. **Google Analytics**:
   - Idź do GA4 → Raporty → Czas rzeczywisty
   - Odwiedź swoją stronę
   - Sprawdź czy widzisz aktywność

2. **Google Tag Manager**:
   - Idź do GTM → Podgląd
   - Odwiedź swoją stronę
   - Sprawdź czy tagi się uruchamiają

---

## 🎉 GRATULACJE!

Wszystkie integracje są skonfigurowane i działają! 

### 📊 **Co masz teraz:**
- ✅ Działające formularze z Web3Forms
- ✅ Pełne śledzenie Google Analytics 4
- ✅ Zaawansowany Google Tag Manager
- ✅ Responsywny kalendarz rezerwacji
- ✅ Cross-browser compatibility
- ✅ Automatyczne testy

### 🚀 **Następne kroki:**
1. Przetestuj wszystko dokładnie
2. Dodaj prawdziwe dane retreatów
3. Skonfiguruj płatności (Stripe/PayU)
4. Dodaj więcej custom events w Analytics
5. Stwórz kampanie reklamowe w Google Ads

---

## 📞 **Potrzebujesz pomocy?**

Jeśli coś nie działa:
1. Sprawdź console w przeglądarce (F12)
2. Sprawdź czy wszystkie zmienne środowiskowe są ustawione
3. Sprawdź czy serwer deweloperski działa
4. Uruchom testy cross-browser

**Powodzenia! 🧘‍♀️✨**
