'use client';

import React from 'react';
import Link from 'next/link';
import { HeroTitle, SubTitle, StatNumber, StatLabel, Badge } from '@/components/ui/UnifiedTypography';
import { SecondaryButton } from '@/components/ui/UnifiedButton';

const MinimalistHero = React.memo(function MinimalistHero() {

  return (
    <>
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .hero-content {
          animation: fadeInUp var(--duration-entrance) var(--ease-smooth) 0.2s both;
        }

        .hero-badge {
          animation: fadeInUp var(--duration-slow) var(--ease-smooth) 0.4s both;
        }

        .hero-title {
          animation: fadeInUp var(--duration-entrance) var(--ease-smooth) 0.6s both;
        }

        .hero-subtitle {
          animation: fadeInUp var(--duration-slow) var(--ease-smooth) 0.8s both;
        }

        .hero-description {
          animation: fadeInUp var(--duration-slow) var(--ease-smooth) 1s both;
        }

        .hero-stats {
          animation: fadeInUp var(--duration-entrance) var(--ease-smooth) 1.2s both;
        }

        .hero-buttons {
          animation: fadeInUp var(--duration-slow) var(--ease-smooth) 1.4s both;
        }

        .hero-form {
          animation: fadeInUp var(--duration-slow) var(--ease-smooth) 1.6s both;
        }
      `}</style>
      
      <section
        className="relative min-h-screen flex items-center justify-center overflow-hidden"
        style={{
          paddingTop: "120px",
          backgroundColor: "#FCF6EE"
        }}
      >
        {/* Background image */}
        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: "url('/images/background/bali-hero.webp')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundAttachment: "scroll",
            backgroundRepeat: "no-repeat"
          }}
        />

        {/* Overlay gradient */}
        <div
          className="absolute inset-0 z-10"
          style={{
            background: `linear-gradient(180deg, rgba(252,246,238,0.3) 0%, rgba(255,255,255,0.6) 70%, rgba(255,255,255,0.85) 100%)`
          }}
        />

        {/* Texture overlay */}
        <div
          className="absolute inset-0 z-10 opacity-10"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            mixBlendMode: 'multiply'
          }}
        />

        {/* Main content */}
        <div className="hero-content relative z-10 text-center max-w-5xl mx-auto px-6">
          
          {/* Badge */}
          <div className="hero-badge inline-block mb-6">
            <Badge variant="outline" className="text-[11px] tracking-[3.5px]">
              RETREATY JOGI • BALI & SRI LANKA
            </Badge>
          </div>

          {/* Title */}
          <div className="hero-title">
            <HeroTitle>BAKASANA</HeroTitle>
          </div>

          {/* Subtitle */}
          <div className="hero-subtitle">
            <SubTitle>~ jóga jest drogą ciszy ~</SubTitle>
          </div>

          {/* Description */}
          <p className="hero-description text-[clamp(1rem,2.5vw,1.125rem)] text-charcoal-light max-w-[620px] mx-auto leading-[1.75] font-normal mb-16">
            Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
          </p>

          {/* Statistics */}
          <div className="hero-stats grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-20 max-w-4xl mx-auto mb-16">
            <div className="text-center" itemScope itemType="https://schema.org/EducationalOccupationalCredential">
              <StatNumber itemProp="credentialLevel">200h</StatNumber>
              <StatLabel itemProp="name">CERTYFIKACJA YTT</StatLabel>
            </div>
            <div className="text-center">
              <StatNumber>7</StatNumber>
              <StatLabel>LAT DOŚWIADCZENIA</StatLabel>
            </div>
            <div className="text-center">
              <StatNumber>150+</StatNumber>
              <StatLabel>ZADOWOLONYCH UCZESTNIKÓW</StatLabel>
            </div>
            <div className="text-center" itemScope itemType="https://schema.org/AggregateRating">
              <StatNumber itemProp="ratingValue">4.9</StatNumber>
              <StatLabel>ŚREDNIA OCEN</StatLabel>
              <meta itemProp="bestRating" content="5" />
              <meta itemProp="reviewCount" content="150" />
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="hero-buttons flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
            <SecondaryButton
              as={Link}
              href="/program"
              size="lg"
              className="hover-lift hover-glow"
            >
              PRZEGLĄD HARMONOGRAMU
            </SecondaryButton>

            <a
              href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center font-light tracking-[2px] hover-lift focus-ring px-12 py-4 text-sm bg-enterprise-brown hover:bg-enterprise-brown/90 text-white rounded-full transition-all duration-300 hover:scale-105"
              aria-label="Skontaktuj się przez WhatsApp"
            >
              KONTAKT WHATSAPP
            </a>

            <SecondaryButton
              as={Link}
              href="/rezerwacja"
              size="lg"
              className="hover-lift hover-glow"
            >
              REZERWUJ KONSULTACJĘ
            </SecondaryButton>
          </div>
        </div>

        {/* Side form */}
        <div className="hero-form hidden xl:block absolute right-8 top-1/2 transform -translate-y-1/2 z-10 bg-sanctuary/95 backdrop-blur-sm p-8 max-w-sm w-80 border border-enterprise-brown/10"
        style={{
          boxShadow: '0 8px 32px rgba(139, 115, 85, 0.08)'
        }}>
          <h3 className="text-xl font-cormorant text-charcoal mb-2">Gotowa na transformację?</h3>
          <p className="text-sm text-charcoal-light mb-6">Rozpocznij swoją duchową podróż</p>
          <div className="space-y-4">
            <input
              type="email"
              placeholder="Twój email"
              className="w-full px-0 py-3 border-0 border-b border-stone-light text-sm focus:outline-none focus:border-enterprise-brown transition-colors duration-300 bg-transparent"
              disabled
            />
            <Link
              href="/kontakt"
              className="w-full px-6 py-3 bg-enterprise-brown text-sanctuary font-light tracking-[2px] hover:bg-terra hover-lift focus-ring flex items-center justify-center"
              style={{ fontSize: '13px' }}
            >
              KONTAKT →
            </Link>
          </div>
        </div>
      </section>
    </>
  );
});

export default MinimalistHero;