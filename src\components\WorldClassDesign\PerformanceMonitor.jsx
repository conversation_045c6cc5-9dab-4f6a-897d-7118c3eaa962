'use client';

import React, { useEffect, useState } from 'react';

/**
 * 📊 PERFORMANCE MONITOR - TOP 1% DESIGN FEATURE
 * Monitorowanie wydajności w czasie rzeczywistym
 * Inspirowane przez Chrome DevTools, Lighthouse
 */
const PerformanceMonitor = ({ 
  enabled = process.env.NODE_ENV === 'development',
  showWidget = true,
  thresholds = {
    lcp: 2500,
    fid: 100,
    cls: 0.1,
    fcp: 1800,
    ttfb: 800,
  },
  className = '',
  ...props 
}) => {
  const [metrics, setMetrics] = useState({
    lcp: 0,
    fid: 0,
    cls: 0,
    fcp: 0,
    ttfb: 0,
    memory: 0,
    fps: 0,
    loadTime: 0,
  });

  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const measurePerformance = () => {
      // Basic timing
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');
      
      setMetrics(prev => ({
        ...prev,
        loadTime: navigation?.loadEventEnd - navigation?.fetchStart || 0,
        fcp: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        ttfb: navigation?.responseStart - navigation?.fetchStart || 0,
      }));

      // Memory usage
      if (performance.memory) {
        setMetrics(prev => ({
          ...prev,
          memory: performance.memory.usedJSHeapSize / 1024 / 1024, // MB
        }));
      }
    };

    // Performance Observer for Core Web Vitals
    if ('PerformanceObserver' in window) {
      // LCP
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        setMetrics(prev => ({
          ...prev,
          lcp: lastEntry.startTime,
        }));
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // FID
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const firstEntry = entries[0];
        setMetrics(prev => ({
          ...prev,
          fid: firstEntry.processingStart - firstEntry.startTime,
        }));
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // CLS
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        setMetrics(prev => ({
          ...prev,
          cls: clsValue,
        }));
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // FPS monitoring
      let lastTime = performance.now();
      let frameCount = 0;
      
      const measureFPS = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          setMetrics(prev => ({
            ...prev,
            fps: Math.round(frameCount * 1000 / (currentTime - lastTime)),
          }));
          frameCount = 0;
          lastTime = currentTime;
        }
        
        requestAnimationFrame(measureFPS);
      };
      
      requestAnimationFrame(measureFPS);
    }

    // Initial measurement
    measurePerformance();
    
    // Periodic updates
    const interval = setInterval(measurePerformance, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [enabled]);

  const getScoreColor = (metric, value) => {
    const threshold = thresholds[metric];
    if (!threshold) return '#22c55e'; // green
    
    if (metric === 'cls') {
      if (value <= 0.1) return '#22c55e'; // green
      if (value <= 0.25) return '#f59e0b'; // yellow
      return '#ef4444'; // red
    }
    
    if (value <= threshold * 0.8) return '#22c55e'; // green
    if (value <= threshold) return '#f59e0b'; // yellow
    return '#ef4444'; // red
  };

  const formatMetric = (metric, value) => {
    switch (metric) {
      case 'memory':
        return `${value.toFixed(1)} MB`;
      case 'fps':
        return `${value} FPS`;
      case 'cls':
        return value.toFixed(3);
      case 'lcp':
      case 'fid':
      case 'fcp':
      case 'ttfb':
      case 'loadTime':
        return `${Math.round(value)} ms`;
      default:
        return Math.round(value);
    }
  };

  const getPerformanceScore = () => {
    const scores = {
      lcp: metrics.lcp <= thresholds.lcp ? 100 : Math.max(0, 100 - (metrics.lcp - thresholds.lcp) / 10),
      fid: metrics.fid <= thresholds.fid ? 100 : Math.max(0, 100 - (metrics.fid - thresholds.fid) / 2),
      cls: metrics.cls <= thresholds.cls ? 100 : Math.max(0, 100 - (metrics.cls - thresholds.cls) * 200),
      fcp: metrics.fcp <= thresholds.fcp ? 100 : Math.max(0, 100 - (metrics.fcp - thresholds.fcp) / 10),
      ttfb: metrics.ttfb <= thresholds.ttfb ? 100 : Math.max(0, 100 - (metrics.ttfb - thresholds.ttfb) / 5),
    };
    
    return Math.round(Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length);
  };

  if (!enabled || !showWidget) return null;

  return (
    <div
      className={`fixed bottom-4 right-4 z-50 ${className}`}
      style={{ fontFamily: 'monospace' }}
      {...props}
    >
      {/* Compact widget */}
      <div
        className="bg-black/90 text-white p-3 rectangular shadow-xl cursor-pointer transition-all duration-200 hover:bg-black/95"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rectangular"
            style={{ backgroundColor: getScoreColor('overall', getPerformanceScore()) }}
          />
          <span className="text-sm font-medium">
            {getPerformanceScore()}
          </span>
          <span className="text-xs opacity-75">
            {metrics.fps} FPS
          </span>
        </div>
      </div>

      {/* Expanded widget */}
      {isExpanded && (
        <div className="absolute bottom-full right-0 mb-2 bg-black/95 text-white p-4 rectangular shadow-xl min-w-[300px]">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold">Performance Monitor</h3>
            <button
              onClick={() => setIsExpanded(false)}
              className="text-gray-400 hover:text-white"
            >
              ×
            </button>
          </div>

          <div className="space-y-2">
            {/* Core Web Vitals */}
            <div className="border-b border-gray-700 pb-2 mb-2">
              <div className="text-xs text-gray-400 mb-1">Core Web Vitals</div>
              {[
                { key: 'lcp', label: 'LCP', desc: 'Largest Contentful Paint' },
                { key: 'fid', label: 'FID', desc: 'First Input Delay' },
                { key: 'cls', label: 'CLS', desc: 'Cumulative Layout Shift' },
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between py-1">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rectangular"
                      style={{ backgroundColor: getScoreColor(key, metrics[key]) }}
                    />
                    <span className="text-xs" title={desc}>{label}</span>
                  </div>
                  <span className="text-xs font-mono">
                    {formatMetric(key, metrics[key])}
                  </span>
                </div>
              ))}
            </div>

            {/* Other metrics */}
            <div>
              <div className="text-xs text-gray-400 mb-1">Other Metrics</div>
              {[
                { key: 'fcp', label: 'FCP', desc: 'First Contentful Paint' },
                { key: 'ttfb', label: 'TTFB', desc: 'Time to First Byte' },
                { key: 'loadTime', label: 'Load', desc: 'Load Time' },
                { key: 'memory', label: 'Memory', desc: 'Memory Usage' },
                { key: 'fps', label: 'FPS', desc: 'Frames Per Second' },
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between py-1">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rectangular"
                      style={{ backgroundColor: getScoreColor(key, metrics[key]) }}
                    />
                    <span className="text-xs" title={desc}>{label}</span>
                  </div>
                  <span className="text-xs font-mono">
                    {formatMetric(key, metrics[key])}
                  </span>
                </div>
              ))}
            </div>

            {/* Performance score */}
            <div className="border-t border-gray-700 pt-2 mt-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-400">Performance Score</span>
                <span
                  className="text-sm font-semibold"
                  style={{ color: getScoreColor('overall', getPerformanceScore()) }}
                >
                  {getPerformanceScore()}/100
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(PerformanceMonitor);