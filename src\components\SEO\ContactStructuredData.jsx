'use client';

import { useEffect } from 'react';

/**
 * Contact Structured Data Component
 * Adds schema.org structured data for contact information
 */
const ContactStructuredData = ({ 
  phone = '***********',
  email = '<EMAIL>',
  availableLanguages = ['Polish', 'English']
}) => {
  useEffect(() => {
    // Add Contact Point structured data
    const contactStructuredData = {
      "@context": "https://schema.org",
      "@type": "ContactPoint",
      "telephone": `+${phone}`,
      "email": email,
      "contactType": "customer service",
      "availableLanguage": availableLanguages,
      "contactOption": ["TollFree", "HearingImpairedSupported"],
      "areaServed": ["PL", "EU"],
      "serviceType": "Yoga Retreats Booking",
      "description": "Kontakt w sprawie rezerwacji retreatów jogi na Bali i Sri Lanka"
    };

    // Add WhatsApp Contact structured data
    const whatsappContactData = {
      "@context": "https://schema.org",
      "@type": "ContactPoint",
      "telephone": `+${phone}`,
      "contactType": "customer service",
      "availableLanguage": availableLanguages,
      "contactOption": ["TollFree"],
      "areaServed": ["PL", "EU"],
      "serviceType": "WhatsApp Support",
      "description": "Szybki kontakt przez WhatsApp - odpowiadamy w ciągu 24h",
      "url": `https://wa.me/${phone}`,
      "sameAs": `https://wa.me/${phone}`
    };

    // Create script elements
    const contactScript = document.createElement('script');
    contactScript.type = 'application/ld+json';
    contactScript.innerHTML = JSON.stringify(contactStructuredData);
    
    const whatsappScript = document.createElement('script');
    whatsappScript.type = 'application/ld+json';
    whatsappScript.innerHTML = JSON.stringify(whatsappContactData);

    // Add to head
    document.head.appendChild(contactScript);
    document.head.appendChild(whatsappScript);

    // Cleanup
    return () => {
      try {
        document.head.removeChild(contactScript);
        document.head.removeChild(whatsappScript);
      } catch (e) {
        // Scripts might have been removed already
      }
    };
  }, [phone, email, availableLanguages]);

  return null; // This component doesn't render anything
};

export default ContactStructuredData;