'use client';

import React from 'react';
import { cn } from '@/lib/utils';

// Import all Lucide icons we use
import {
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  User,
  Users,
  Heart,
  Star,
  Check,
  Plus,
  Minus,
  Search,
  Filter,
  Settings,
  Home,
  Info,
  AlertCircle,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Download,
  Upload,
  Share,
  Copy,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Globe,
  Wifi,
  WifiOff,
  Battery,
  Sun,
  Moon,
  Cloud,
  Zap,
  Target,
  Award,
  Gift,
  Camera,
  Image,
  Video,
  Music,
  Headphones,
  Mic,
  MicOff,
  MessageCircle,
  MessageSquare,
  Send,
  Bookmark,
  BookmarkCheck,
  Flag,
  Tag,
  Folder,
  File,
  FileText,
  Link,
  Paperclip,
  Scissors,
  Printer,
  Save,
  RefreshCw,
  RotateCcw,
  RotateCw,
  Maximize,
  Minimize,
  Move,
  Resize,
  Crop,
  Layers,
  Grid,
  List,
  BarChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  ShoppingCart,
  Package,
  Truck,
  Plane,
  Car,
  Bike,
  Walk,
  Navigation,
  Compass,
  Map,
  Building,
  Store,
  Coffee,
  Utensils,
  Wine,
  Pizza,
  Apple,
  Leaf,
  Flower,
  Tree,
  Mountain,
  Waves,
  Sunrise,
  Sunset,
  Rainbow,
  Snowflake,
  Umbrella,
  Shield,
  ShieldCheck,
  Key,
  Fingerprint,
  Scan,
  QrCode,
  Barcode,
  Bluetooth,
  Usb,
  HardDrive,
  Cpu,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Desktop,
  Watch,
  Gamepad2,
  Headset,
  Keyboard,
  Mouse,
  Printer as PrinterIcon,
  Scanner,
  Router,
  Server,
  Database,
  Cloud as CloudIcon,
  Wifi as WifiIcon,
  Signal,
  Antenna,
  Radio,
  Tv,
  Speaker,
  Volume,
  VolumeX as VolumeOffIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  Camera as CameraIcon,
  Video as VideoIcon,
  Image as ImageIcon,
  Film,
  Music as MusicIcon,
  Disc,
  Cassette,
  Radio as RadioIcon,
  Podcast,
  Rss,
  Newspaper,
  Book,
  BookOpen,
  Library,
  GraduationCap,
  School,
  Briefcase,
  Building2,
  Factory,
  Warehouse,
  Hospital,
  Cross,
  Pill,
  Stethoscope,
  Thermometer,
  Activity,
  Zap as ZapIcon,
  Heart as HeartIcon,
  Brain,
  Eye as EyeIcon,
  Ear,
  Hand,
  Footprints,
  Baby,
  Child,
  UserCheck,
  UserPlus,
  UserMinus,
  UserX,
  Users as UsersIcon,
  Crown,
  Medal,
  Trophy,
  Ribbon,
  Star as StarIcon,
  Sparkles,
  Flame,
  Droplet,
  Snowflake as SnowflakeIcon,
  Wind,
  Tornado,
  Cloudy,
  CloudRain,
  CloudSnow,
  CloudLightning,
  Sun as SunIcon,
  Moon as MoonIcon,
  Stars,
  Eclipse,
  Comet,
  Rocket,
  Satellite,
  Telescope,
  Microscope,
  Atom,
  Dna,
  Magnet,
  Zap as ElectricIcon,
  Battery as BatteryIcon,
  Plug,
  Power,
  PowerOff,
  RotateCcw as RefreshIcon,
  Loader,
  Loader2,
  MoreHorizontal,
  MoreVertical,
  Ellipsis,
  Dot,
  Circle,
  Square,
  Triangle,
  Hexagon,
  Pentagon,
  Octagon,
  Diamond,
  Hash,
  AtSign,
  Percent,
  DollarSign as DollarIcon,
  Euro,
  PoundSterling,
  Yen,
  Bitcoin,
  Banknote,
  Coins,
  Wallet,
  CreditCard as CardIcon,
  Receipt,
  Calculator,
  Abacus,
  PiggyBank,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  BarChart as BarChartIcon,
  LineChart,
  PieChart as PieChartIcon,
  AreaChart,
  ScatterChart,
  Gauge,
  Speedometer,
  Timer,
  Stopwatch,
  Clock as ClockIcon,
  Calendar as CalendarIcon,
  CalendarDays,
  CalendarCheck,
  CalendarX,
  CalendarPlus,
  CalendarMinus,
  AlarmClock,
  Hourglass,
  Sunrise as SunriseIcon,
  Sunset as SunsetIcon,
  CloudSun,
  CloudMoon,
  Thermometer as ThermometerIcon,
  Gauge as GaugeIcon,
  Wind as WindIcon,
  Droplets,
  Umbrella as UmbrellaIcon,
  Rainbow as RainbowIcon,
  Tornado as TornadoIcon,
  Volcano,
  Earthquake,
  Tsunami,
  Flood,
  Drought,
  Wildfire,
  Avalanche,
  Landslide,
  Sinkhole,
  Crater,
  Geyser,
  HotSprings,
  Glacier,
  Iceberg,
  Desert,
  Oasis,
  Forest,
  Jungle,
  Savanna,
  Prairie,
  Meadow,
  Valley,
  Canyon,
  Cliff,
  Cave,
  Tunnel,
  Bridge,
  Road,
  Highway,
  Railway,
  Airport,
  Harbor,
  Lighthouse,
  Castle,
  Church,
  Mosque,
  Temple,
  Synagogue,
  Pagoda,
  Torii,
  Statue,
  Monument,
  Fountain,
  Garden,
  Park,
  Playground,
  Stadium,
  Theater,
  Cinema,
  Museum,
  Gallery,
  Library as LibraryIcon,
  University,
  School as SchoolIcon,
  Kindergarten,
  Daycare,
  Nursery,
  Orphanage,
  Retirement,
  Nursing,
  Hospice,
  Clinic,
  Pharmacy,
  Laboratory,
  Research,
  Observatory,
  Planetarium,
  Aquarium,
  Zoo,
  Safari,
  Farm,
  Ranch,
  Stable,
  Barn,
  Silo,
  Windmill,
  Mill,
  Factory as FactoryIcon,
  Plant,
  Refinery,
  PowerPlant,
  SolarPanel,
  WindTurbine,
  Dam,
  WaterTower,
  Reservoir,
  Well,
  Pipeline,
  OilRig,
  Mine,
  Quarry,
  Excavator,
  Bulldozer,
  Crane,
  Forklift,
  Tractor,
  Combine,
  Harvester,
  Plow,
  Seeder,
  Sprayer,
  Mower,
  Chainsaw,
  Axe,
  Hammer,
  Wrench,
  Screwdriver,
  Pliers,
  Saw,
  Drill,
  Level,
  Ruler,
  Tape,
  Compass as CompassIcon,
  Protractor,
  Triangle as TriangleIcon,
  Square as SquareIcon,
  Circle as CircleIcon,
  Hexagon as HexagonIcon,
  Pentagon as PentagonIcon,
  Octagon as OctagonIcon,
  Diamond as DiamondIcon,
  Heart as HeartShapeIcon,
  Star as StarShapeIcon,
  Cross as CrossIcon,
  Plus as PlusIcon,
  Minus as MinusIcon,
  X as XIcon,
  Check as CheckIcon,
  ArrowUp as ArrowUpIcon,
  ArrowDown as ArrowDownIcon,
  ArrowLeft as ArrowLeftIcon,
  ArrowRight as ArrowRightIcon,
  ChevronUp as ChevronUpIcon,
  ChevronDown as ChevronDownIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  ChevronsUp,
  ChevronsDown,
  ChevronsLeft,
  ChevronsRight,
  ArrowUpLeft,
  ArrowUpRight,
  ArrowDownLeft,
  ArrowDownRight,
  Move as MoveIcon,
  MousePointer,
  MousePointer2,
  Hand as HandIcon,
  Grab,
  GrabHand,
  PointerHand,
  Peace,
  ThumbsUp,
  ThumbsDown,
  Clap,
  Wave,
  Salute,
  Fist,
  FingerPrint,
  Palmprint,
  Footprint,
  Pawprint,
  Hoofprint,
  Tire,
  Wheel,
  Gear,
  Cog,
  Settings as SettingsIcon,
  Sliders,
  Knob,
  Switch,
  Toggle,
  Button,
  Joystick,
  Remote,
  Controller,
  Gamepad,
  Dice1,
  Dice2,
  Dice3,
  Dice4,
  Dice5,
  Dice6,
  Spade,
  Club,
  Diamond as DiamondSuit,
  Heart as HeartSuit,
  Puzzle,
  Jigsaw,
  Rubik,
  Chess,
  Checkers,
  Backgammon,
  Domino,
  Mahjong,
  Poker,
  Blackjack,
  Roulette,
  Slot,
  Lottery,
  Bingo,
  Keno,
  Scratch,
  Ticket,
  Trophy as TrophyIcon,
  Medal as MedalIcon,
  Ribbon as RibbonIcon,
  Crown as CrownIcon,
  Laurel,
  Wreath,
  Badge,
  Shield as ShieldIcon,
  Sword,
  Dagger,
  Bow,
  Arrow,
  Spear,
  Axe as AxeIcon,
  Mace,
  Flail,
  Whip,
  Staff,
  Wand,
  Scepter,
  Orb,
  Crystal,
  Gem,
  Pearl,
  Ring,
  Necklace,
  Bracelet,
  Earring,
  Brooch,
  Pin,
  Cufflink,
  Watch as WatchIcon,
  Clock as ClockWatchIcon,
  Stopwatch as StopwatchIcon,
  Timer as TimerIcon,
  Hourglass as HourglassIcon,
  Sundial,
  Metronome,
  Pendulum,
  Spring,
  Gear as GearIcon,
  Pulley,
  Lever,
  Screw,
  Bolt,
  Nut,
  Washer,
  Rivet,
  Pin as PinIcon,
  Nail,
  Staple,
  Clip,
  Clamp,
  Vise,
  Anvil,
  Forge,
  Bellows,
  Crucible,
  Mold,
  Cast,
  Weld,
  Solder,
  Braze,
  Cut,
  Grind,
  Polish,
  Sand,
  File,
  Rasp,
  Chisel,
  Gouge,
  Plane,
  Router,
  Lathe,
  Mill as MillIcon,
  Drill as DrillIcon,
  Press,
  Punch,
  Shear,
  Bend,
  Roll,
  Stamp,
  Emboss,
  Engrave,
  Etch,
  Laser,
  Plasma,
  Water,
  Abrasive,
  Chemical,
  Thermal,
  Electrical,
  Mechanical,
  Hydraulic,
  Pneumatic,
  Magnetic,
  Optical,
  Acoustic,
  Ultrasonic,
  Infrared,
  Ultraviolet,
  Xray,
  Gamma,
  Alpha,
  Beta,
  Neutron,
  Proton,
  Electron,
  Photon,
  Quark,
  Lepton,
  Boson,
  Fermion,
  Hadron,
  Baryon,
  Meson,
  Neutrino,
  Muon,
  Tau,
  Pion,
  Kaon,
  Lambda,
  Sigma,
  Xi,
  Omega,
  Delta,
  Theta,
  Phi,
  Psi,
  Chi,
  Eta,
  Rho,
  Upsilon,
  Zeta,
  Kappa,
  Nu,
  Omicron,
  Pi,
  Alpha as AlphaIcon,
  Beta as BetaIcon,
  Gamma as GammaIcon,
  Delta as DeltaIcon,
  Epsilon,
  Zeta as ZetaIcon,
  Eta as EtaIcon,
  Theta as ThetaIcon,
  Iota,
  Kappa as KappaIcon,
  Lambda as LambdaIcon,
  Mu,
  Nu as NuIcon,
  Xi as XiIcon,
  Omicron as OmicronIcon,
  Pi as PiIcon,
  Rho as RhoIcon,
  Sigma as SigmaIcon,
  Tau as TauIcon,
  Upsilon as UpsilonIcon,
  Phi as PhiIcon,
  Chi as ChiIcon,
  Psi as PsiIcon,
  Omega as OmegaIcon,
} from 'lucide-react';

/**
 * BAKASANA UNIFIED ICON SYSTEM
 * Single source of truth for all icons with consistent sizing and colors
 * Using design-tokens.css for unified styling
 */

// Unified icon sizes using design tokens
const iconSizes = {
  xs: 'w-3 h-3',      // 12px - Extra small icons
  sm: 'w-4 h-4',      // 16px - Small icons
  md: 'w-5 h-5',      // 20px - Default icons  
  lg: 'w-6 h-6',      // 24px - Large icons
  xl: 'w-8 h-8',      // 32px - Extra large icons
  '2xl': 'w-10 h-10', // 40px - Hero icons
  '3xl': 'w-12 h-12', // 48px - Feature icons
};

// Unified icon colors using design tokens
const iconColors = {
  primary: 'text-enterprise-brown',
  secondary: 'text-temple-gold',
  muted: 'text-stone',
  subtle: 'text-sage',
  contrast: 'text-charcoal',
  light: 'text-stone-light',
  accent: 'text-sand',
  warm: 'text-amber',
  neutral: 'text-ash',
  inherit: 'text-current',
  // Extended warm & friendly colors
  cream: 'text-cream',
  peach: 'text-warm-peach',
  softSage: 'text-soft-sage',
  terracotta: 'text-terracotta',
  blush: 'text-blush',
  coral: 'text-friendly-coral',
  lavender: 'text-calm-lavender',
};

// Icon registry - maps icon names to components
const iconRegistry = {
  // Navigation
  menu: Menu,
  close: X,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'external-link': ExternalLink,
  
  // Communication
  mail: Mail,
  phone: Phone,
  'map-pin': MapPin,
  'message-circle': MessageCircle,
  'message-square': MessageSquare,
  send: Send,
  
  // Time & Calendar
  calendar: Calendar,
  clock: Clock,
  
  // User & People
  user: User,
  users: Users,
  
  // Actions
  heart: Heart,
  star: Star,
  check: Check,
  plus: Plus,
  minus: Minus,
  search: Search,
  filter: Filter,
  settings: Settings,
  
  // Status
  home: Home,
  info: Info,
  'alert-circle': AlertCircle,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  
  // Media
  play: Play,
  pause: Pause,
  'volume-2': Volume2,
  'volume-x': VolumeX,
  
  // File Operations
  download: Download,
  upload: Upload,
  share: Share,
  copy: Copy,
  edit: Edit,
  'trash-2': Trash2,
  
  // Visibility
  eye: Eye,
  'eye-off': EyeOff,
  
  // Security
  lock: Lock,
  unlock: Unlock,
  
  // Connectivity
  globe: Globe,
  wifi: Wifi,
  'wifi-off': WifiOff,
  
  // System
  battery: Battery,
  sun: Sun,
  moon: Moon,
  cloud: Cloud,
  zap: Zap,
  
  // Business
  target: Target,
  award: Award,
  gift: Gift,
  
  // Media Content
  camera: Camera,
  image: Image,
  video: Video,
  music: Music,
  headphones: Headphones,
  mic: Mic,
  'mic-off': MicOff,
  
  // Organization
  bookmark: Bookmark,
  'bookmark-check': BookmarkCheck,
  flag: Flag,
  tag: Tag,
  folder: Folder,
  file: File,
  'file-text': FileText,
  link: Link,
  paperclip: Paperclip,
  
  // Tools
  scissors: Scissors,
  printer: Printer,
  save: Save,
  'refresh-cw': RefreshCw,
  'rotate-ccw': RotateCcw,
  'rotate-cw': RotateCw,
  
  // Layout
  maximize: Maximize,
  minimize: Minimize,
  move: Move,
  resize: Resize,
  crop: Crop,
  layers: Layers,
  grid: Grid,
  list: List,
  
  // Charts & Analytics
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  'trending-up': TrendingUp,
  'trending-down': TrendingDown,
  
  // Commerce
  'dollar-sign': DollarSign,
  'credit-card': CreditCard,
  'shopping-cart': ShoppingCart,
  package: Package,
  
  // Transportation
  truck: Truck,
  plane: Plane,
  car: Car,
  bike: Bike,
  walk: Walk,
  navigation: Navigation,
  compass: Compass,
  map: Map,
  
  // Places
  building: Building,
  store: Store,
  
  // Food & Drink
  coffee: Coffee,
  utensils: Utensils,
  wine: Wine,
  pizza: Pizza,
  apple: Apple,
  
  // Nature
  leaf: Leaf,
  flower: Flower,
  tree: Tree,
  mountain: Mountain,
  waves: Waves,
  sunrise: Sunrise,
  sunset: Sunset,
  rainbow: Rainbow,
  snowflake: Snowflake,
  umbrella: Umbrella,
  
  // More icons can be added as needed...
};

/**
 * Unified Icon Component
 * 
 * @param {string} name - Icon name from the registry
 * @param {string} size - Icon size (xs, sm, md, lg, xl, 2xl, 3xl)
 * @param {string} color - Icon color theme
 * @param {string} className - Additional CSS classes
 * @param {object} props - Additional props passed to the icon component
 */
export default function Icon({ 
  name, 
  size = 'md', 
  color = 'primary',
  className = '', 
  ...props 
}) {
  const IconComponent = iconRegistry[name];
  
  if (!IconComponent) {
    // Fallback for unknown icons
    console.warn(`Icon "${name}" not found in registry. Available icons:`, Object.keys(iconRegistry));
    return (
      <div 
        className={`${iconSizes[size]} ${iconColors[color]} bg-current/10 rounded ${className}`}
        title={`Missing icon: ${name}`}
        {...props}
      />
    );
  }
  
  return (
    <IconComponent 
      className={`${iconSizes[size]} ${iconColors[color]} ${className}`} 
      {...props} 
    />
  );
}

/**
 * Icon with consistent styling for specific use cases
 */
export function NavigationIcon({ name, active = false, ...props }) {
  return (
    <Icon
      name={name}
      size="md"
      color={active ? 'primary' : 'muted'}
      className={`transition-colors duration-normal ${active ? 'text-enterprise-brown' : 'hover:text-enterprise-brown'}`}
      {...props}
    />
  );
}

export function ButtonIcon({ name, size = 'sm', ...props }) {
  return (
    <Icon
      name={name}
      size={size}
      color="inherit"
      {...props}
    />
  );
}

export function StatusIcon({ name, status = 'default', ...props }) {
  const statusColors = {
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-blue-600',
    default: 'text-sage',
  };
  
  return (
    <Icon
      name={name}
      size="sm"
      className={statusColors[status]}
      {...props}
    />
  );
}

export function FeatureIcon({ name, ...props }) {
  return (
    <Icon
      name={name}
      size="2xl"
      color="primary"
      className="mb-4"
      {...props}
    />
  );
}

// Export the icon registry for external use
export { iconRegistry };
