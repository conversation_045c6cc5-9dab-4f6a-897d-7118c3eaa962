'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';

const OnlineClassesSection = React.memo(() => {
  const features = useMemo(() => [
    {
      id: 'individual',
      icon: '◆',
      title: '<PERSON><PERSON><PERSON><PERSON> indywidualne',
      description: 'Praktyka dostosowana do Twoich potrzeb i poziomu'
    },
    {
      id: 'group',
      icon: '◇',
      title: '<PERSON>aj<PERSON>cia grupowe',
      description: '<PERSON><PERSON> w małej grupie z innymi'
    },
    {
      id: 'flexibility',
      icon: '◊',
      title: 'Z domu',
      description: 'Praktykuj w swoim czasie i miejscu'
    }
  ], []);

  return (
    <section className="online-section" style={{ background: '#FDF9F3', padding: '120px 0' }}>
      <div className="container">
        {/* DIVIDER W STYLU BAKASANA */}
        <div className="bakasana-intro-divider">
          <div className="bakasana-intro-diamond"></div>
        </div>
        
        <div className="text-center mb-20">
          <h2 className="online-title" style={{ 
            fontFamily: '<PERSON><PERSON><PERSON><PERSON>, serif',
            fontSize: '48px',
            fontWeight: '400',
            color: '#2C2928',
            marginBottom: '20px'
          }}>
            Zajęcia Online
          </h2>
          <p className="online-subtitle" style={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '18px',
            color: '#9B9592',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Joga online - indywidualnie lub w grupie
          </p>
        </div>

        <div className="online-features">
          {features.map((feature) => (
            <div key={feature.id} className="online-feature">
              <div className="feature-icon">
                {feature.icon}
              </div>
              <h3 className="feature-title">
                {feature.title}
              </h3>
              <p className="feature-description">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <Link 
            href="/zajecia-online" 
            className="bakasana-intro-button"
            aria-label="Dowiedz się więcej o zajęciach online"
          >
            Sprawdź zajęcia online →
          </Link>
        </div>
      </div>
    </section>
  );
});

OnlineClassesSection.displayName = 'OnlineClassesSection';

export default OnlineClassesSection;