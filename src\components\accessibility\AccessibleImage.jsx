'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { useAccessibility } from './AccessibilityProvider';

/**
 * ♿ ACCESSIBLE IMAGE COMPONENT
 * 
 * WCAG 2.1 AA compliant image component with:
 * - Comprehensive alt text support
 * - Decorative image handling
 * - Loading state announcements
 * - Error handling with fallbacks
 * - Context-aware descriptions
 * - Screen reader optimizations
 */

const AccessibleImage = ({
  src,
  alt,
  width,
  height,
  decorative = false,
  longDescription = '',
  caption = '',
  priority = false,
  quality = 85,
  className = '',
  sizes = '100vw',
  fill = false,
  loading = 'lazy',
  onLoad,
  onError,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const { announce } = useAccessibility();
  const imageRef = useRef(null);

  // Validate alt text for non-decorative images
  useEffect(() => {
    if (!decorative && (!alt || alt.trim() === '')) {
      console.warn('AccessibleImage: Non-decorative image missing alt text', { src });
    }
  }, [alt, decorative, src]);

  // Handle image load
  const handleLoad = (e) => {
    setIsLoading(false);
    setIsLoaded(true);
    setHasError(false);
    
    // Announce to screen readers if important image
    if (!decorative && priority) {
      announce(`Obraz załadowany: ${alt}`);
    }
    
    onLoad?.(e);
  };

  // Handle image error
  const handleError = (e) => {
    setIsLoading(false);
    setHasError(true);
    setIsLoaded(false);
    
    // Announce error to screen readers
    if (!decorative) {
      announce(`Błąd ładowania obrazu: ${alt || 'Obraz niedostępny'}`);
    }
    
    onError?.(e);
  };

  // Generate appropriate alt text
  const getAltText = () => {
    if (decorative) return '';
    if (hasError) return `Błąd ładowania obrazu: ${alt || 'Obraz niedostępny'}`;
    return alt || '';
  };

  // Generate ARIA attributes
  const getAriaAttributes = () => {
    const attrs = {};
    
    if (decorative) {
      attrs['aria-hidden'] = 'true';
      attrs.role = 'presentation';
    } else {
      if (longDescription) {
        attrs['aria-describedby'] = `img-desc-${src.replace(/\W/g, '')}`;
      }
      if (caption) {
        attrs['aria-labelledby'] = `img-caption-${src.replace(/\W/g, '')}`;
      }
    }
    
    return attrs;
  };

  // Error fallback component
  const ErrorFallback = () => (
    <div 
      className={`bg-stone-light/20 border-2 border-dashed border-stone-light flex items-center justify-center text-stone text-sm p-4 ${className}`}
      style={fill ? { width: '100%', height: '100%' } : { width, height }}
      role={decorative ? 'presentation' : 'img'}
      aria-label={decorative ? undefined : getAltText()}
    >
      <div className="text-center">
        <svg 
          className="w-8 h-8 mx-auto mb-2 text-stone" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
          />
        </svg>
        <p>Obraz niedostępny</p>
        {!decorative && alt && (
          <p className="text-xs mt-1 text-stone-light">{alt}</p>
        )}
      </div>
    </div>
  );

  // Loading placeholder
  const LoadingPlaceholder = () => (
    <div 
      className={`bg-stone-light/10 animate-pulse ${className}`}
      style={fill ? { width: '100%', height: '100%' } : { width, height }}
      role={decorative ? 'presentation' : 'img'}
      aria-label={decorative ? undefined : 'Ładowanie obrazu...'}
    />
  );

  if (hasError) {
    return <ErrorFallback />;
  }

  const imageProps = {
    src,
    alt: getAltText(),
    quality,
    onLoad: handleLoad,
    onError: handleError,
    className: `${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`,
    loading: priority ? 'eager' : loading,
    priority,
    sizes,
    ...getAriaAttributes(),
    ...props
  };

  return (
    <figure className={decorative ? undefined : 'accessible-image-figure'}>
      <div className="relative overflow-hidden">
        {isLoading && <LoadingPlaceholder />}
        
        {fill ? (
          <Image
            {...imageProps}
            fill
            style={{ objectFit: 'cover' }}
            ref={imageRef}
          />
        ) : (
          <Image
            {...imageProps}
            width={width}
            height={height}
            ref={imageRef}
          />
        )}
      </div>
      
      {/* Caption */}
      {caption && !decorative && (
        <figcaption 
          id={`img-caption-${src.replace(/\W/g, '')}`}
          className="text-sm text-stone mt-2 text-center"
        >
          {caption}
        </figcaption>
      )}
      
      {/* Long description */}
      {longDescription && !decorative && (
        <div 
          id={`img-desc-${src.replace(/\W/g, '')}`}
          className="sr-only"
        >
          {longDescription}
        </div>
      )}
    </figure>
  );
};

// Specialized accessible image components
export const AccessibleHeroImage = ({ src, alt, className = '', priority = true, ...props }) => (
  <AccessibleImage
    src={src}
    alt={alt}
    fill
    priority={priority}
    quality={90}
    sizes="100vw"
    className={className}
    {...props}
  />
);

export const AccessibleGalleryImage = ({ src, alt, className = '', caption, ...props }) => (
  <AccessibleImage
    src={src}
    alt={alt}
    width={400}
    height={300}
    quality={85}
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    className={`rectangular ${className}`}
    caption={caption}
    {...props}
  />
);

export const DecorativeImage = ({ src, className = '', ...props }) => (
  <AccessibleImage
    src={src}
    alt=""
    decorative={true}
    className={className}
    {...props}
  />
);

export default AccessibleImage;
