"use client"; // Ta dyrektywa jest kluczowa dla komponentów klienckich

import Script from 'next/script';
// The 'useEffect' import was here but not used directly in this component.
// It's good practice to remove unused imports.

const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

export default function GoogleAnalytics() {
  if (!GA_TRACKING_ID) {
    // It's helpful to log a warning if the ID is missing, for easier debugging.
    console.warn('Google Analytics Measurement ID (NEXT_PUBLIC_GA_MEASUREMENT_ID) is not set. Google Analytics will not be initialized.');
    return null;
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        onLoad={() => {
          console.log('GTag script (gtag.js) loaded successfully.');
        }}
        onError={(e) => {
          console.error('Error loading GTag script (gtag.js):', e);
        }}
      />
      <Script
        id="google-analytics-config-script" // Using a more unique ID for this inline script
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
              send_page_view: true,
              allow_google_signals: true,
              allow_ad_personalization_signals: true,
              cookie_flags: 'SameSite=None;Secure',
              custom_map: {
                'custom_parameter_1': 'user_type',
                'custom_parameter_2': 'page_category',
                'custom_parameter_3': 'engagement_level'
              }
            });

            // Enhanced ecommerce tracking
            gtag('config', '${GA_TRACKING_ID}', {
              currency: 'PLN',
              country: 'PL'
            });

            // Custom events for yoga retreat business
            window.trackRetreatInterest = function(retreatName, price) {
              gtag('event', 'retreat_interest', {
                event_category: 'Retreats',
                event_label: retreatName,
                value: price,
                currency: 'PLN'
              });
            };

            window.trackFormStart = function(formType) {
              gtag('event', 'form_start', {
                event_category: 'Forms',
                event_label: formType
              });
            };

            window.trackFormComplete = function(formType) {
              gtag('event', 'form_complete', {
                event_category: 'Forms',
                event_label: formType
              });
            };

            window.trackVideoPlay = function(videoTitle) {
              gtag('event', 'video_play', {
                event_category: 'Video',
                event_label: videoTitle
              });
            };

            window.trackScrollDepth = function(percentage) {
              gtag('event', 'scroll', {
                event_category: 'Engagement',
                event_label: percentage + '%'
              });
            };

            console.log('Enhanced Google Analytics configured with ID:', '${GA_TRACKING_ID}');
          `,
        }}
      />
    </>
  );
}