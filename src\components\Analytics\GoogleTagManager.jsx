'use client';

import Script from 'next/script';

export default function GoogleTagManager() {
  const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID;

  if (!GTM_ID) {
    console.warn('Google Tag Manager ID not found. Please set NEXT_PUBLIC_GTM_ID in your environment variables.');
    return null;
  }

  return (
    <>
      {/* Google Tag Manager Script */}
      <Script
        id="google-tag-manager"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');

            // Initialize dataLayer with page info
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              'page_type': 'website',
              'page_category': 'yoga_retreat',
              'business_type': 'wellness',
              'country': 'Poland',
              'language': 'pl'
            });

            // Custom GTM events for Bakasana
            window.gtmTrackEvent = function(eventName, eventData) {
              window.dataLayer.push({
                'event': eventName,
                ...eventData
              });
            };

            // Retreat-specific tracking
            window.gtmTrackRetreatView = function(retreatName, price, location) {
              window.dataLayer.push({
                'event': 'retreat_view',
                'retreat_name': retreatName,
                'retreat_price': price,
                'retreat_location': location,
                'currency': 'PLN'
              });
            };

            window.gtmTrackRetreatBooking = function(retreatName, price, step) {
              window.dataLayer.push({
                'event': 'retreat_booking',
                'retreat_name': retreatName,
                'retreat_price': price,
                'booking_step': step,
                'currency': 'PLN'
              });
            };

            // Form tracking
            window.gtmTrackFormInteraction = function(formName, action, field) {
              window.dataLayer.push({
                'event': 'form_interaction',
                'form_name': formName,
                'form_action': action,
                'form_field': field
              });
            };

            // Enhanced ecommerce for retreats
            window.gtmTrackPurchase = function(transactionId, retreatName, price) {
              window.dataLayer.push({
                'event': 'purchase',
                'transaction_id': transactionId,
                'value': price,
                'currency': 'PLN',
                'items': [{
                  'item_id': retreatName.toLowerCase().replace(/\s+/g, '_'),
                  'item_name': retreatName,
                  'category': 'yoga_retreat',
                  'quantity': 1,
                  'price': price
                }]
              });
            };

            // User engagement tracking
            window.gtmTrackEngagement = function(engagementType, value) {
              window.dataLayer.push({
                'event': 'user_engagement',
                'engagement_type': engagementType,
                'engagement_value': value
              });
            };

            console.log('Google Tag Manager initialized with ID:', '${GTM_ID}');
          `,
        }}
      />

      {/* Google Tag Manager NoScript */}
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  );
}

// Hook for easy GTM tracking in components
export const useGTMTracking = () => {
  const trackEvent = (eventName, eventData) => {
    if (typeof window !== 'undefined' && window.gtmTrackEvent) {
      window.gtmTrackEvent(eventName, eventData);
    }
  };

  const trackRetreatView = (retreatName, price, location) => {
    if (typeof window !== 'undefined' && window.gtmTrackRetreatView) {
      window.gtmTrackRetreatView(retreatName, price, location);
    }
  };

  const trackRetreatBooking = (retreatName, price, step) => {
    if (typeof window !== 'undefined' && window.gtmTrackRetreatBooking) {
      window.gtmTrackRetreatBooking(retreatName, price, step);
    }
  };

  const trackFormInteraction = (formName, action, field) => {
    if (typeof window !== 'undefined' && window.gtmTrackFormInteraction) {
      window.gtmTrackFormInteraction(formName, action, field);
    }
  };

  const trackPurchase = (transactionId, retreatName, price) => {
    if (typeof window !== 'undefined' && window.gtmTrackPurchase) {
      window.gtmTrackPurchase(transactionId, retreatName, price);
    }
  };

  const trackEngagement = (engagementType, value) => {
    if (typeof window !== 'undefined' && window.gtmTrackEngagement) {
      window.gtmTrackEngagement(engagementType, value);
    }
  };

  return {
    trackEvent,
    trackRetreatView,
    trackRetreatBooking,
    trackFormInteraction,
    trackPurchase,
    trackEngagement
  };
};
