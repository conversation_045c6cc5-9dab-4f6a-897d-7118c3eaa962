/**
 * 🎭 BAKASANA - ADVANCED ANIMATIONS HOOK
 * 
 * Comprehensive animation system with:
 * - Intersection Observer for scroll reveals
 * - Magnetic cursor effects
 * - Parallax scrolling
 * - Performance optimizations
 * - Accessibility compliance
 * 
 * Inspired by award-winning agencies like Apple, Stripe, Linear
 */

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';

// ===== INTERSECTION OBSERVER HOOK =====
export function useScrollReveal(options = {}) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);
  
  const defaultOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
    triggerOnce: true,
    ...options
  };
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (defaultOptions.triggerOnce) {
            observer.unobserve(element);
          }
        } else if (!defaultOptions.triggerOnce) {
          setIsVisible(false);
        }
      },
      {
        threshold: defaultOptions.threshold,
        rootMargin: defaultOptions.rootMargin,
      }
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [defaultOptions.rootMargin, defaultOptions.threshold, defaultOptions.triggerOnce]);
  
  return [elementRef, isVisible];
}

// ===== STAGGERED ANIMATIONS HOOK =====
export function useStaggeredReveal(childSelector = '.reveal-stagger', delay = 100) {
  const containerRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          const children = container.querySelectorAll(childSelector);
          children.forEach((child, index) => {
            setTimeout(() => {
              child.classList.add('revealed');
            }, index * delay);
          });
          observer.unobserve(container);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    );
    
    observer.observe(container);
    
    return () => observer.disconnect();
  }, [childSelector, delay]);
  
  return [containerRef, isVisible];
}

// ===== MAGNETIC CURSOR HOOK =====
export function useMagneticCursor(strength = 0.3) {
  const elementRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const handleMouseMove = (e) => {
      if (!isHovered) return;
      
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;
      
      element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    };
    
    const handleMouseEnter = () => {
      setIsHovered(true);
      element.style.transition = 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)';
    };
    
    const handleMouseLeave = () => {
      setIsHovered(false);
      element.style.transform = 'translate(0, 0)';
      element.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [strength, isHovered]);
  
  return [elementRef, isHovered];
}

// ===== PARALLAX SCROLL HOOK =====
export function useParallax(speed = 0.5) {
  const elementRef = useRef(null);
  const [offset, setOffset] = useState(0);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + scrolled;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;
      
      // Only calculate parallax when element is in viewport
      if (scrolled + windowHeight > elementTop && scrolled < elementTop + elementHeight) {
        const yPos = -(scrolled - elementTop) * speed;
        element.style.transform = `translateY(${yPos}px)`;
      }
    };
    
    // Throttle scroll event for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };
    
    window.addEventListener('scroll', throttledScroll, { passive: true });
    
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [speed]);
  
  return [elementRef, offset];
}

// ===== MOUSE FOLLOW HOOK =====
export function useMouseFollow(smoothness = 0.1) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [smoothPosition, setSmoothPosition] = useState({ x: 0, y: 0 });
  
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);
  
  useEffect(() => {
    const animate = () => {
      setSmoothPosition((prev) => ({
        x: prev.x + (mousePosition.x - prev.x) * smoothness,
        y: prev.y + (mousePosition.y - prev.y) * smoothness,
      }));
      
      requestAnimationFrame(animate);
    };
    
    animate();
  }, [mousePosition, smoothness]);
  
  return smoothPosition;
}

// ===== SCROLL PROGRESS HOOK =====
export function useScrollProgress() {
  const [progress, setProgress] = useState(0);
  
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setProgress(scrollPercent);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return progress;
}

// ===== VIEWPORT SIZE HOOK =====
export function useViewportSize() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return size;
}

// ===== SMOOTH SCROLL TO HOOK =====
export function useSmoothScrollTo() {
  const scrollTo = useCallback((target, options = {}) => {
    const element = typeof target === 'string' 
      ? document.querySelector(target) 
      : target;
    
    if (!element) return;
    
    const defaultOptions = {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
      ...options
    };
    
    element.scrollIntoView(defaultOptions);
  }, []);
  
  return scrollTo;
}

// ===== INTERSECTION OBSERVER PERFORMANCE HOOK =====
export function useIntersectionObserver(options = {}) {
  const [entries, setEntries] = useState([]);
  const observer = useRef(null);
  
  const defaultOptions = useMemo(() => ({
    threshold: 0.1,
    rootMargin: '0px',
    ...options
  }), [options]);
  
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => setEntries(entries),
      defaultOptions
    );
    
    return () => observer.current?.disconnect();
  }, [defaultOptions]);
  
  const observe = useCallback((element) => {
    if (observer.current && element) {
      observer.current.observe(element);
    }
  }, []);
  
  const unobserve = useCallback((element) => {
    if (observer.current && element) {
      observer.current.unobserve(element);
    }
  }, []);
  
  return { entries, observe, unobserve };
}

// ===== ACCESSIBILITY AWARE ANIMATIONS =====
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handleChange = (e) => setPrefersReducedMotion(e.matches);
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  return prefersReducedMotion;
}

// ===== ENHANCED BUTTON INTERACTIONS =====
export function useButtonInteractions() {
  const buttonRef = useRef(null);
  
  useEffect(() => {
    const button = buttonRef.current;
    if (!button) return;
    
    const handleMouseMove = (e) => {
      const rect = button.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      
      button.style.setProperty('--mouse-x', `${x}%`);
      button.style.setProperty('--mouse-y', `${y}%`);
    };
    
    const handleMouseEnter = () => {
      button.classList.add('hover');
    };
    
    const handleMouseLeave = () => {
      button.classList.remove('hover');
    };
    
    button.addEventListener('mousemove', handleMouseMove);
    button.addEventListener('mouseenter', handleMouseEnter);
    button.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      button.removeEventListener('mousemove', handleMouseMove);
      button.removeEventListener('mouseenter', handleMouseEnter);
      button.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);
  
  return buttonRef;
}

// ===== EXPORT ALL HOOKS =====
const animationHooks = {
  useScrollReveal,
  useStaggeredReveal,
  useMagneticCursor,
  useParallax,
  useMouseFollow,
  useScrollProgress,
  useViewportSize,
  useSmoothScrollTo,
  useIntersectionObserver,
  useReducedMotion,
  useButtonInteractions
};

export default animationHooks;