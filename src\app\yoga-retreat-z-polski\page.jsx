// Metadata export for Next.js
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Plane, MapPin, Users, Star, Clock, ArrowRight, 
  Phone, Mail, Instagram, Check, Calendar, Heart, 
  Shield, Award, Globe, Zap, Coffee, Mountain, 
  Palmtree, Waves, Sun, Camera, Utensils
} from 'lucide-react';

export const metadata = {
  title: 'Yoga Retreat z Polski 2025 - Najlepsze Oferty Bali & Sri Lanka | BAKASANA',
  description: '🇵🇱 Yoga retreat z Polski 2025 - Bali i Sri Lanka z polską opieką! Certyfikowana instruktorka, małe grupy, all-inclusive. ✈️ Loty z Warszawy, Krakowa, Gdańska. 4.9/5 ⭐',
  keywords: 'yoga retreat polska, yoga retreat z polski, retreat jogi z polski, bali z polski, sri lanka z polski, yoga wakacje polska, polskie retreaty jogi',
  openGraph: {
    title: 'Yoga Retreat z Polski 2025 - Najle<PERSON>ze Oferty | BAKASANA',
    description: '🇵🇱 Yoga retreat z Polski - Bali i Sri Lanka z polską opieką! Certyfikowana instruktorka, all-inclusive. 4.9/5 ⭐',
    images: ['/images/og/yoga-retreat-z-polski-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/yoga-retreat-z-polski',
  },
};

const YogaRetreatZPolski = () => {
  const polishAdvantages = [
    {
      icon: <Globe className="w-5 h-5 text-temple" />,
      title: "Polska instruktorka",
      description: "Julia Jakubowicz - rodzima instruktorka jogi"
    },
    {
      icon: <Users className="w-5 h-5 text-temple" />,
      title: "Polska grupa",
      description: "Poznaj nowych przyjaciół z Polski"
    },
    {
      icon: <Shield className="w-5 h-5 text-temple" />,
      title: "Bezpieczeństwo",
      description: "Pełna opieka przez cały pobyt"
    },
    {
      icon: <Heart className="w-5 h-5 text-temple" />,
      title: "Bez barier językowych",
      description: "Wszystko w języku polskim"
    }
  ];

  const destinations = [
    {
      name: "Bali",
      duration: "7-14 dni",
      price: "3400 PLN",
      highlights: [
        "Ubud - tarasy ryżowe",
        "Gili Air - rajskie plaże", 
        "Świątynie hinduistyczne",
        "Ayurveda masaże",
        "Warung food tours"
      ],
      image: "/images/destinations/bali-retreat-polish.webp",
      badge: "Najpopularniejszy",
      badgeColor: "bg-temple"
    },
    {
      name: "Sri Lanka",
      duration: "10-14 dni",
      price: "3800 PLN",
      highlights: [
        "Sigiriya - Lwia Skała",
        "Kandy - górskie miasta",
        "Ella - plantacje herbaty",
        "Ayurveda authentic",
        "Południowe plaże"
      ],
      image: "/images/destinations/srilanka-retreat-polish.webp",
      badge: "Dla odkrywców",
      badgeColor: "bg-golden"
    }
  ];

  const includedServices = [
    {
      category: "Transport",
      items: [
        "Assistance przy locie z Polski",
        "Odbiór z lotniska",
        "Transport lokalny",
        "Pomoc z przesiadkami"
      ]
    },
    {
      category: "Zakwaterowanie",
      items: [
        "Hotele 4-5 gwiazdek",
        "Pokoje klimatyzowane",
        "WiFi w całym hotelu",
        "Śniadania w cenie"
      ]
    },
    {
      category: "Joga & Wellness",
      items: [
        "Daily joga w języku polskim",
        "Medytacja mindfulness",
        "Ayurveda masaże",
        "Breathing workshops"
      ]
    },
    {
      category: "Kultura & Zwiedzanie",
      items: [
        "Przewodnik po świątyniach",
        "Local food experiences",
        "Cultural immersion",
        "Photography workshops"
      ]
    }
  ];

  const polishCities = [
    { city: "Warszawa", airport: "WAW", price: "Od 2800 PLN" },
    { city: "Kraków", airport: "KRK", price: "Od 2900 PLN" },
    { city: "Gdańsk", airport: "GDN", price: "Od 3100 PLN" },
    { city: "Wrocław", airport: "WRO", price: "Od 3000 PLN" },
    { city: "Katowice", airport: "KTW", price: "Od 2950 PLN" },
    { city: "Poznań", airport: "POZ", price: "Od 3050 PLN" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-temple/10 to-golden/10">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/destinations/polish-group-yoga.webp"
            alt="Yoga retreat z Polski - grupa podczas praktyki"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4">
          <div className="text-center">
            <Badge className="mb-6 bg-white/90 text-temple border-temple/30 px-4 py-2">
              🇵🇱 Yoga Retreat z Polski 2025
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-charcoal mb-6">
              Najlepsze Yoga Retreat <br />
              <span className="text-temple">z Polski</span>
            </h1>
            
            <p className="text-xl text-wood mb-8 max-w-3xl mx-auto">
              Odkryj magię jogi na Bali i Sri Lanka z polską opieką! 
              Certyfikowana instruktorka Julia Jakubowicz, małe grupy, 
              all-inclusive pakiety. Loty z głównych miast Polski.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button 
                size="lg" 
                className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
                asChild
              >
                <Link href="/rezerwacja">
                  Zarezerwuj z Polski
                  <Plane className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              
              <Button 
                size="lg" 
                variant="outline" 
                className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
                asChild
              >
                <Link href="/program">
                  Zobacz Programy
                </Link>
              </Button>
            </div>
            
            <div className="flex flex-wrap items-center justify-center gap-6 text-wood">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-golden fill-golden" />
                <span className="font-semibold">4.9/5</span>
                <span>(127 polskich uczestników)</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-temple" />
                <span>Pełna opieka</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-5 h-5 text-temple" />
                <span>Język polski</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Poland */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Dlaczego wybierać yoga retreat z Polski?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Podróżuj z rodakami, bez barier językowych, z pełną opieką 
              i wsparciem na każdym kroku egzotycznej przygody.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {polishAdvantages.map((advantage, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-temple/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    {advantage.icon}
                  </div>
                  <h3 className="font-semibold text-charcoal mb-2">{advantage.title}</h3>
                  <p className="text-sm text-wood">{advantage.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Destinations */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Wybierz swój kierunek
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Dwa wyjątkowe miejsca na yoga retreat z Polski. 
              Każdy kierunek oferuje unikalne doświadczenia.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-8">
            {destinations.map((destination, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20 overflow-hidden">
                <div className="relative h-64">
                  <Image
                    src={destination.image}
                    alt={`Yoga retreat ${destination.name} z Polski`}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className={`${destination.badgeColor} text-white`}>
                      {destination.badge}
                    </Badge>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-2xl font-bold text-charcoal">{destination.name}</h3>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-temple">{destination.price}</div>
                      <div className="text-sm text-wood">{destination.duration}</div>
                    </div>
                  </div>
                  
                  <div className="space-y-3 mb-6">
                    {destination.highlights.map((highlight, hIndex) => (
                      <div key={hIndex} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-temple flex-shrink-0" />
                        <span className="text-wood text-sm">{highlight}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-3">
                    <Button 
                      className="flex-1 bg-temple hover:bg-temple/90"
                      asChild
                    >
                      <Link href={`/program?destination=${destination.name.toLowerCase()}`}>
                        Szczegóły
                      </Link>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="flex-1 border-temple text-temple hover:bg-temple/10"
                      asChild
                    >
                      <Link href="/rezerwacja">
                        Rezerwuj
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Flight Options */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Loty z Polski - wszystkie główne miasta
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Organizujemy loty z największych miast Polski. 
              Pomoc w organizacji, grupowe przesiadki, wsparcie 24/7.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {polishCities.map((city, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-charcoal text-lg">{city.city}</h3>
                      <p className="text-wood text-sm">Lotnisko {city.airport}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-temple font-bold">{city.price}</div>
                      <div className="text-xs text-wood">+ retreat</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2">
                      <Plane className="w-4 h-4 text-temple" />
                      <span className="text-sm text-wood">Loty grupowe</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-temple" />
                      <span className="text-sm text-wood">Assistance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-temple" />
                      <span className="text-sm text-wood">Pomoc z przesiadkami</span>
                    </div>
                  </div>
                  
                  <Button 
                    size="sm" 
                    className="w-full bg-temple hover:bg-temple/90"
                    asChild
                  >
                    <Link href={`/rezerwacja?city=${city.city.toLowerCase()}`}>
                      Sprawdź terminy
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Co zawiera pakiet retreat z Polski?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Kompleksowa opieka od momentu wyjazdu z Polski 
              do bezpiecznego powrotu do domu.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {includedServices.map((service, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-charcoal mb-4 text-center">
                    {service.category}
                  </h3>
                  <div className="space-y-3">
                    {service.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-temple flex-shrink-0 mt-0.5" />
                        <span className="text-wood text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Polish Community */}
      <section className="py-16 bg-temple/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-6">
                Dołącz do polskiej społeczności BAKASANA
              </h2>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">127 Polaków już doświadczyło transformacji</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Star className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">4.9/5 średnia ocena od polskich uczestników</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Heart className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">Dożywotnie przyjaźnie i wspólne wartości</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-temple/10 rounded-full flex items-center justify-center">
                    <Globe className="w-4 h-4 text-temple" />
                  </div>
                  <span className="text-wood">Polskie wsparcie na każdym kroku</span>
                </div>
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 border border-temple/20">
                <h3 className="font-semibold text-charcoal mb-3">Specjalne ceny dla Polaków:</h3>
                <div className="flex items-center gap-4 mb-3">
                  <span className="text-3xl font-bold text-temple">Od 3400 PLN</span>
                  <Badge className="bg-golden/20 text-golden">
                    Early Bird -300 PLN
                  </Badge>
                </div>
                <p className="text-sm text-wood">
                  Ceny all-inclusive z lotem z Polski. Możliwość rozłożenia płatności na raty.
                </p>
              </div>
            </div>
            
            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="relative aspect-square rounded-lg overflow-hidden">
                    <Image
                      src="/images/community/polish-group-bali.webp"
                      alt="Polska grupa podczas retreatu na Bali"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="relative aspect-square rounded-lg overflow-hidden">
                    <Image
                      src="/images/community/polish-group-dinner.webp"
                      alt="Polscy uczestnicy podczas wspólnego posiłku"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-4 mt-8">
                  <div className="relative aspect-square rounded-lg overflow-hidden">
                    <Image
                      src="/images/community/polish-group-yoga.webp"
                      alt="Polska grupa podczas praktyki jogi"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="relative aspect-square rounded-lg overflow-hidden">
                    <Image
                      src="/images/community/polish-group-temple.webp"
                      alt="Polscy uczestnicy w świątyni"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-temple/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-4">
            Gotowy na yoga retreat z Polski?
          </h2>
          <p className="text-wood mb-8 max-w-2xl mx-auto">
            Dołącz do nas na wyjątkowym yoga retreat z polską opieką. 
            Terminy 2025 dostępne - ograniczona liczba miejsc!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button 
              size="lg" 
              className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj z Polski
                <Plane className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
              asChild
            >
              <Link href="/kontakt">
                Zadaj Pytanie
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-wood">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Instagram className="w-4 h-4" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "TravelAgency",
            "name": "BAKASANA - Yoga Retreat z Polski",
            "description": "Najlepsze yoga retreat z Polski na Bali i Sri Lanka z certyfikowaną polską instruktorką",
            "url": "https://bakasana-travel.blog/yoga-retreat-z-polski",
            "image": "https://bakasana-travel.blog/images/og/yoga-retreat-z-polski-2025.jpg",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "PL"
            },
            "offers": [
              {
                "@type": "Offer",
                "name": "Yoga Retreat Bali z Polski",
                "price": "3400",
                "priceCurrency": "PLN",
                "availability": "https://schema.org/InStock"
              },
              {
                "@type": "Offer",
                "name": "Yoga Retreat Sri Lanka z Polski", 
                "price": "3800",
                "priceCurrency": "PLN",
                "availability": "https://schema.org/InStock"
              }
            ],
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "127"
            }
          })
        }}
      />
    </div>
  );
};

export default YogaRetreatZPolski;