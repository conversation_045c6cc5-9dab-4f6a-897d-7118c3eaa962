'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedCard - Ujednolicony system kart BAKASANA
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

const cardVariants = {
  // DEFAULT - Standardowe karty with breathing
  default: {
    base: "card-breathing",
    hover: "", // Handled by card-breathing class
    focus: "focus-within:shadow-focus"
  },

  // ELEVATED - Karty z większym naciskiem
  elevated: {
    base: "bg-sanctuary shadow-elegant border border-rose-gold",
    hover: "hover:shadow-premium hover:border-rose-gold-warm hover:-translate-y-2 transition-all duration-normal breathe",
    focus: "focus-within:shadow-focus"
  },

  // MINIMAL - Ultra-subtelne karty
  minimal: {
    base: "bg-transparent border-0",
    hover: "hover:bg-whisper transition-all duration-normal breathe-gentle",
    focus: "focus-within:shadow-focus"
  },

  // WARM - Ciepłe, organiczne karty with breathing
  warm: {
    base: "bg-linen border border-whisper card-breathing",
    hover: "", // Handled by card-breathing class
    focus: "focus-within:shadow-focus"
  },

  // NEW - Breathing variant
  breathing: {
    base: "card-breathing breathe",
    hover: "", // Handled by card-breathing and breathe classes
    focus: "focus-within:shadow-focus"
  }
};

const paddingVariants = {
  none: "p-0",
  sm: "p-4",
  md: "p-6", 
  lg: "p-8",
  xl: "p-12"
};

export default function UnifiedCard({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  ...props
}) {
  const variantStyles = cardVariants[variant];
  const paddingStyles = paddingVariants[padding];
  
  return (
    <div
      className={cn(
        // Base styles - Old Money elegance using unified design tokens
        "transition-all duration-normal ease-smooth",
        "overflow-hidden", // Dla organicznych elementów

        // Variant styles
        variantStyles.base,
        variantStyles.hover,
        variantStyles.focus,

        // Padding
        paddingStyles,

        // Custom className
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Komponenty strukturalne karty

export function CardHeader({ children, className = '', ...props }) {
  return (
    <div 
      className={cn("mb-6", className)} 
      {...props}
    >
      {children}
    </div>
  );
}

export function CardTitle({ children, className = '', level = 3, ...props }) {
  const Tag = `h${level}`;

  return (
    <Tag
      className={cn(
        "font-montserrat font-light text-charcoal leading-tight",
        level === 1 && "text-4xl mb-4",
        level === 2 && "text-3xl mb-3",
        level === 3 && "text-2xl mb-3",
        level === 4 && "text-xl mb-2",
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function CardDescription({ children, className = '', ...props }) {
  return (
    <p
      className={cn(
        "text-sage leading-relaxed font-lato font-light",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function CardContent({ children, className = '', ...props }) {
  return (
    <div 
      className={cn("space-y-4", className)} 
      {...props}
    >
      {children}
    </div>
  );
}

export function CardFooter({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "mt-6 pt-6 border-t border-stone-light/20 flex items-center justify-between",
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
}

// Wyspecjalizowane warianty kart

export function RetreatCard({ children, ...props }) {
  return (
    <UnifiedCard variant="elevated" padding="lg" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function TestimonialCard({ children, ...props }) {
  return (
    <UnifiedCard variant="warm" padding="lg" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function ServiceCard({ children, ...props }) {
  return (
    <UnifiedCard variant="default" padding="md" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function MinimalCard({ children, ...props }) {
  return (
    <UnifiedCard variant="minimal" padding="md" {...props}>
      {children}
    </UnifiedCard>
  );
}