'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ScrollReveal } from './ScrollReveal';

export default function NewsletterSignup({ 
  variant = 'default', 
  className = '',
  showLeadMagnet = true 
}) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !email.includes('@')) {
      setStatus('❌ Podaj prawidłowy adres email');
      setTimeout(() => setStatus(''), 3000);
      return;
    }

    setIsSubmitting(true);
    setStatus('Zapisywanie...');

    try {
      // ConvertKit API integration
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          tags: ['bali-guide-download', 'website-signup'],
          source: 'website'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setShowSuccess(true);
        setEmail('');
        setStatus('');
        
        // Trigger download after successful signup
        if (showLeadMagnet) {
          setTimeout(() => {
            window.open('/downloads/przewodnik-po-bali.pdf', '_blank');
          }, 1000);
        }
      } else {
        throw new Error(result.error || 'Błąd zapisu');
      }
    } catch (error) {
      console.error('Newsletter signup error:', error);
      setStatus('❌ Wystąpił błąd. Spróbuj ponownie.');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 5000);
    }
  };

  if (variant === 'popup') {
    return (
      <AnimatePresence>
        {!showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-white rectangular p-8 max-w-md w-full shadow-2xl"
            >
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">🏝️</div>
                <h3 className="text-2xl font-serif text-enterprise-brown mb-2">
                  Darmowy Przewodnik po Bali
                </h3>
                <p className="text-wood-light text-sm">
                  Pobierz 20-stronicowy PDF z najlepszymi miejscami na Bali
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Twój adres email"
                  className="w-full px-4 py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
                  required
                />
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-unified-primary"
                >
                  {isSubmitting ? 'Wysyłanie...' : 'Pobierz Przewodnik'}
                </button>
                
                {status && (
                  <p className="text-sm text-center text-enterprise-brown">{status}</p>
                )}
              </form>
              
              <p className="text-xs text-wood-light/60 text-center mt-4">
                Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.
              </p>
            </motion.div>
          </motion.div>
        )}
        
        {showSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ y: 50 }}
              animate={{ y: 0 }}
              className="bg-white rectangular p-8 max-w-md w-full shadow-2xl text-center"
            >
              <div className="text-6xl mb-4">✅</div>
              <h3 className="text-2xl font-serif text-enterprise-brown mb-4">
                Dziękujemy!
              </h3>
              <p className="text-wood-light mb-6">
                Przewodnik zostanie pobrany automatycznie. 
                Sprawdź też swoją skrzynkę email!
              </p>
              <button
                onClick={() => setShowSuccess(false)}
                className="btn-unified-secondary"
              >
                Zamknij
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  if (variant === 'inline') {
    return (
      <ScrollReveal className={`bg-gradient-to-r from-temple/5 to-golden/5 rectangular p-8 ${className}`}>
        <div className="max-w-2xl mx-auto text-center">
          <div className="text-3xl mb-4">📧</div>
          <h3 className="text-2xl font-serif text-enterprise-brown mb-4">
            Newsletter Bali Yoga Journey
          </h3>
          <p className="text-wood-light mb-6">
            Otrzymuj inspiracje, tips o jodze i ekskluzywne oferty retreatów
          </p>
          
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Twój adres email"
              className="flex-1 px-4 py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
              required
            />
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-unified-primary whitespace-nowrap"
            >
              {isSubmitting ? 'Zapisywanie...' : 'Zapisz się'}
            </button>
          </form>
          
          {status && (
            <p className="text-sm text-enterprise-brown mt-4">{status}</p>
          )}
          
          {showLeadMagnet && (
            <p className="text-xs text-wood-light/60 mt-4">
              🎁 Bonus: Darmowy przewodnik po Bali po zapisaniu się
            </p>
          )}
        </div>
      </ScrollReveal>
    );
  }

  // Default variant - hero section
  return (
    <div className={`text-center ${className}`}>
      <div className="bg-white/10 backdrop-blur-sm rectangular p-8 max-w-lg mx-auto">
        <div className="text-2xl mb-4">🏝️</div>
        <h3 className="text-xl font-serif text-white mb-4">
          Darmowy Przewodnik po Bali
        </h3>
        <p className="text-white/80 text-sm mb-6">
          20 stron najlepszych miejsc, restauracji i sekretnych plaż
        </p>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Twój adres email"
            className="w-full px-4 py-3 bg-white/20 border border-white/30 rectangular text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
            required
          />
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-white text-enterprise-brown px-6 py-3 rectangular font-medium hover:bg-white/90 transition-colors disabled:opacity-50"
          >
            {isSubmitting ? 'Wysyłanie...' : 'Pobierz Przewodnik'}
          </button>
        </form>
        
        {status && (
          <p className="text-sm text-white/90 mt-4">{status}</p>
        )}
        
        <p className="text-xs text-white/60 mt-4">
          Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.
        </p>
      </div>
    </div>
  );
}

// Exit Intent Hook
export function useExitIntent(callback) {
  useEffect(() => {
    let hasTriggered = false;

    const handleMouseLeave = (e) => {
      if (e.clientY <= 0 && !hasTriggered) {
        hasTriggered = true;
        callback();
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [callback]);
}
