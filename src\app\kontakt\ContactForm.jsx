'use client';

import React, { useState } from 'react';

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    retreatInterest: '',
    honeypot: ''
  });
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Validation functions
  const validateField = (name, value) => {
    switch (name) {
      case 'name':
        return value.length < 2 ? 'Imię musi mieć co najmniej 2 znaki' : '';
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? 'Podaj prawidłowy adres email' : '';
      case 'phone':
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{9,}$/;
        return value && !phoneRegex.test(value) ? 'Podaj prawidłowy numer telefonu' : '';
      case 'message':
        return value.length < 10 ? 'Wiadomość musi mieć co najmniej 10 znaków' : '';
      default:
        return '';
    }
  };

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData({ ...formData, [id]: value });

    // Real-time validation
    if (touched[id]) {
      const error = validateField(id, value);
      setErrors({ ...errors, [id]: error });
    }
  };

  const handleBlur = (e) => {
    const { id, value } = e.target;
    setTouched({ ...touched, [id]: true });
    const error = validateField(id, value);
    setErrors({ ...errors, [id]: error });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Honeypot check - jeśli wypełnione, to spam
    if (formData.honeypot) {
      return;
    }

    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(field => {
      if (field !== 'honeypot' && field !== 'phone' && field !== 'retreatInterest') {
        const error = validateField(field, formData[field]);
        if (error) newErrors[field] = error;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setTouched(Object.keys(formData).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
      setStatus('Proszę poprawić błędy w formularzu');
      return;
    }

    setIsSubmitting(true);
    setStatus('Wysyłanie...');

    try {
      const response = await fetch('https://api.web3forms.com/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          access_key: process.env.NEXT_PUBLIC_WEB3FORMS_ACCESS_KEY || 'YOUR_WEB3FORMS_ACCESS_KEY',
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          message: formData.message,
          retreat_interest: formData.retreatInterest,
          subject: `Nowa wiadomość z BAKASANA od ${formData.name}`,
          from_name: 'BAKASANA',
          to_email: '<EMAIL>',
          // Additional metadata
          source: 'contact_form',
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent
        })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('✅ Wiadomość wysłana pomyślnie! Odpowiemy w ciągu 24 godzin.');
        setFormData({ name: '', email: '', phone: '', message: '', retreatInterest: '', honeypot: '' });
        setErrors({});
        setTouched({});

        // Track successful form submission
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'form_submit', {
            event_category: 'Contact',
            event_label: 'Contact Form Success'
          });
        }
      } else {
        throw new Error(result.message || 'Błąd wysyłania');
      }
    } catch (error) {
      console.error('Error:', error);
      setStatus('Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 8000);
    }
  };

  const socialLinks = [
    {
      href: "https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",
      label: "Instagram",
      aria: "Profil na Instagramie"
    },
    {
      href: "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",
      label: "Facebook",
      aria: "Profil na Facebooku"
    },
    {
      href: "mailto:<EMAIL>",
      label: "Email",
      aria: "Kontakt email"
    }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto">
      {/* FORMULARZ KONTAKTOWY - Ultra-minimal */}
      <div className="space-y-8">
        <div className="text-center lg:text-left">
          <h2 className="section-header mb-6">Napisz do nas</h2>
          <p className="body-text opacity-80">
            Każda wiadomość jest dla nas ważna
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div>
            <label htmlFor="name" className="block subtle-text mb-3">Imię *</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${
                errors.name ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'
              } focus:outline-none`}
              placeholder="Twoje imię"
              aria-describedby={errors.name ? 'name-error' : undefined}
            />
            {errors.name && (
              <p id="name-error" className="text-red-500 text-sm mt-2">{errors.name}</p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block subtle-text mb-3">Email *</label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${
                errors.email ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'
              } focus:outline-none`}
              placeholder="<EMAIL>"
              aria-describedby={errors.email ? 'email-error' : undefined}
            />
            {errors.email && (
              <p id="email-error" className="text-red-500 text-sm mt-2">{errors.email}</p>
            )}
          </div>

          <div>
            <label htmlFor="phone" className="block subtle-text mb-3">Telefon (opcjonalnie)</label>
            <input
              type="tel"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${
                errors.phone ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'
              } focus:outline-none`}
              placeholder="+48 123 456 789"
              aria-describedby={errors.phone ? 'phone-error' : undefined}
            />
            {errors.phone && (
              <p id="phone-error" className="text-red-500 text-sm mt-2">{errors.phone}</p>
            )}
          </div>

          <div>
            <label htmlFor="retreatInterest" className="block subtle-text mb-3">Interesuje Cię (opcjonalnie)</label>
            <select
              id="retreatInterest"
              value={formData.retreatInterest}
              onChange={handleChange}
              className="w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-temple-gold focus:outline-none transition-colors text-charcoal"
            >
              <option value="">Wybierz opcję</option>
              <option value="retreat-bali">Retreat na Bali</option>
              <option value="retreat-poland">Retreat w Polsce</option>
              <option value="private-sessions">Sesje indywidualne</option>
              <option value="online-classes">Zajęcia online</option>
              <option value="workshops">Warsztaty</option>
              <option value="other">Inne</option>
            </select>
          </div>

          <div>
            <label htmlFor="message" className="block subtle-text mb-3">Wiadomość *</label>
            <textarea
              id="message"
              value={formData.message}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              rows={6}
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none ${
                errors.message ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'
              } focus:outline-none`}
              placeholder="Podziel się swoimi myślami..."
              aria-describedby={errors.message ? 'message-error' : undefined}
            />
            {errors.message && (
              <p id="message-error" className="text-red-500 text-sm mt-2">{errors.message}</p>
            )}
          </div>

          {/* Honeypot field - ukryte dla ludzi, widoczne dla botów */}
          <input
            type="text"
            id="honeypot"
            name="honeypot"
            value={formData.honeypot}
            onChange={handleChange}
            style={{ display: 'none' }}
            tabIndex="-1"
            autoComplete="off"
          />

          <div className="pt-8">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`btn-ghost btn-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'}
            </button>

            {status && (
              <p className="text-sm text-charcoal/70 font-light mt-4 max-w-xs">
                {status}
              </p>
            )}
          </div>
        </form>
      </div>

      {/* KONTAKT - Ultra-minimal */}
      <div className="space-y-8">
        <div className="text-center lg:text-left">
          <h3 className="section-header mb-6">Znajdź nas</h3>
          <p className="body-text opacity-80 mb-8">
            Połączmy się w przestrzeni cyfrowej
          </p>
        </div>

        <div className="space-y-6">
          {socialLinks.map((link) => (
            <a
              key={link.label}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.aria}
              className="block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left"
            >
              <h4 className="font-light text-charcoal mb-2 tracking-wide text-lg">
                {link.label}
              </h4>
              <p className="text-sm text-stone font-light">
                {link.label === 'Instagram' && 'Codzienne inspiracje'}
                {link.label === 'Facebook' && 'Społeczność BAKASANA'}
                {link.label === 'Email' && 'Bezpośredni kontakt'}
              </p>
            </a>
          ))}
        </div>

        {/* SACRED DIVIDER */}
        <div className="flex items-center justify-center lg:justify-start my-12">
          <div className="flex items-center gap-4 text-temple-gold/60">
            <div className="w-12 h-px bg-temple-gold/30"></div>
            <span className="text-lg opacity-60">ॐ</span>
            <div className="w-12 h-px bg-temple-gold/30"></div>
          </div>
        </div>

        <div className="text-center lg:text-left">
          <p className="text-sm text-stone font-light italic tracking-wide">
            "Każda podróż zaczyna się od jednego kroku..."
          </p>
          <p className="text-xs text-temple-gold font-light tracking-wide uppercase mt-2">
            Om Swastiastu
          </p>
        </div>
      </div>
    </div>
  );
}