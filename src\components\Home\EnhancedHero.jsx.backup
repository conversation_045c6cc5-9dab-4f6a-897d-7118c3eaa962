'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { motion, useScroll, useTransform } from 'framer-motion';

const EnhancedHero = () => {
  const heroRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 550, y: 550 });
  
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ['start start', 'end start']
  });

  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  useEffect(() => {
    setIsLoaded(true);
    
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
          rgba(201, 165, 117, 0.1) 0%, 
          rgba(253, 252, 248, 0.8) 40%, 
          rgba(249, 247, 242, 1) 100%)`
      }}
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div 
          className="absolute w-96 h-96 bg-golden-lotus/5 rounded-full blur-3xl animate-float"
          style={{
            top: '10%',
            left: '10%',
            animationDelay: '0s'
          }}
        />
        <div 
          className="absolute w-80 h-80 bg-temple/5 rounded-full blur-3xl animate-float"
          style={{
            top: '60%',
            right: '15%',
            animationDelay: '2s'
          }}
        />
        <div 
          className="absolute w-64 h-64 bg-sage/5 rounded-full blur-3xl animate-float"
          style={{
            bottom: '20%',
            left: '20%',
            animationDelay: '4s'
          }}
        />
      </div>

      {/* Parallax Background Image */}
      <motion.div
        className="absolute inset-0 z-0"
        style={{ y, opacity }}
      >
        <Image
          src="/images/hero/bali-sunrise-meditation.jpg"
          alt="Sunrise meditation in Bali"
          fill
          className="object-cover object-center opacity-20"
          priority
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-sanctuary/20 via-transparent to-sanctuary/40" />
      </motion.div>

      {/* Sacred Geometry Overlay */}
      <div className="absolute inset-0 z-10 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 1000 1000">
          <defs>
            <pattern id="sacred-geometry" patternUnits="userSpaceOnUse" width="100" height="100">
              <circle cx="50" cy="50" r="2" fill="currentColor" className="text-golden-lotus" />
              <circle cx="25" cy="25" r="1" fill="currentColor" className="text-temple" />
              <circle cx="75" cy="75" r="1" fill="currentColor" className="text-sage" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#sacred-geometry)" />
        </svg>
      </div>

      {/* Main Content */}
      <div className="relative z-20 text-center max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 1, y: 0 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="space-y-8"
        >
          {/* Om Symbol */}
          <motion.div
            initial={{ opacity: 1, scale: 1 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="text-golden-lotus mb-8"
          >
            <svg className="w-16 h-16 mx-auto" viewBox="0 0 100 100" fill="currentColor">
              <path d="M50 20c-5.5 0-10 4.5-10 10s4.5 10 10 10 10-4.5 10-10-4.5-10-10-10zm0 25c-8.3 0-15 6.7-15 15s6.7 15 15 15 15-6.7 15-15-6.7-15-15-15zm0 35c-11 0-20-9-20-20s9-20 20-20 20 9 20 20-9 20-20 20z" />
            </svg>
          </motion.div>

          {/* Main Title */}
          <motion.h1
            initial={{ opacity: 1, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.2 }}
            className="heading-hero text-charcoal mb-6"
          >
            <span className="block">BAKASANA</span>
            <span className="block text-golden-lotus text-2xl md:text-4xl font-light mt-4 tracking-wider">
              Retreaty Jogi • Bali & Sri Lanka
            </span>
          </motion.h1>

          {/* Tagline */}
          <motion.p
            initial={{ opacity: 1, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.4 }}
            className="text-xl md:text-2xl text-charcoal/80 font-primary italic mb-8"
          >
            — gdzie sens spotyka swój rytm —
          </motion.p>

          {/* Description */}
          <motion.p
            initial={{ opacity: 1, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.6 }}
            className="text-lg md:text-xl text-charcoal/70 max-w-3xl mx-auto leading-relaxed mb-12"
          >
            Odkryj transformacyjne retreaty jogi w duchowym sercu Azji. 
            Dołącz do Julii Jakubowicz w podróży przez tarasy ryżowe Ubud, 
            świątynie Bali i tajemnicze krajobrazy Sri Lanki.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 1, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.8 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.a
              href="/program"
              className="btn-primary text-lg px-8 py-4 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                if (window.gtag) {
                  window.gtag('event', 'hero_cta_click', {
                    event_category: 'Hero',
                    event_label: 'Zobacz Program',
                    value: 1
                  });
                }
              }}
            >
              Zobacz Program
              <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.a>
            
            <motion.a
              href="/rezerwacja"
              className="btn-secondary text-lg px-8 py-4 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                if (window.gtag) {
                  window.gtag('event', 'hero_cta_click', {
                    event_category: 'Hero',
                    event_label: 'Rezerwuj Teraz',
                    value: 2
                  });
                }
              }}
            >
              Rezerwuj Teraz
              <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </motion.a>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 1, y: 0 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
      >
        <div className="flex flex-col items-center text-charcoal/60">
          <span className="text-sm mb-2 font-secondary">Odkryj swoją praktykę</span>
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </motion.div>
        </div>
      </motion.div>

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-golden-lotus rounded-full opacity-60"
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.6, 0.8, 0.6]
          }}
          transition={{ duration: 3, repeat: Infinity, delay: 0 }}
        />
        <motion.div
          className="absolute top-1/3 right-1/3 w-1 h-1 bg-temple rounded-full opacity-50"
          animate={{ 
            scale: [1, 1.5, 1],
            opacity: [0.5, 0.7, 0.5]
          }}
          transition={{ duration: 4, repeat: Infinity, delay: 1 }}
        />
        <motion.div
          className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-sage rounded-full opacity-40"
          animate={{ 
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.6, 0.4]
          }}
          transition={{ duration: 5, repeat: Infinity, delay: 2 }}
        />
      </div>
    </section>
  );
};

export default EnhancedHero;