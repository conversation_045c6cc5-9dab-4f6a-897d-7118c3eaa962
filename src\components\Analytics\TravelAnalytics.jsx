'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

class TravelAnalytics {
  constructor() {
    this.isInitialized = false;
    this.userId = null;
    this.sessionId = null;
    this.pageStartTime = null;
    this.interactionBuffer = [];
    this.performanceMetrics = {};
  }

  // Initialize analytics
  init() {
    if (this.isInitialized) return;
    
    this.sessionId = this.generateSessionId();
    this.userId = this.getUserId();
    this.pageStartTime = Date.now();
    
    // Initialize performance monitoring
    this.initPerformanceMonitoring();
    
    // Initialize user behavior tracking
    this.initUserBehaviorTracking();
    
    // Initialize travel-specific tracking
    this.initTravelTracking();
    
    this.isInitialized = true;
    console.log('Travel Analytics initialized');
  }

  // Generate unique session ID
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get or create user ID
  getUserId() {
    let userId = localStorage.getItem('travel_user_id');
    if (!userId) {
      userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('travel_user_id', userId);
    }
    return userId;
  }

  // Initialize performance monitoring
  initPerformanceMonitoring() {
    // Core Web Vitals
    this.observeWebVitals();
    
    // Page load performance
    this.trackPageLoad();
    
    // Resource loading
    this.trackResourceLoading();
    
    // Memory usage
    this.trackMemoryUsage();
  }

  // Observe Web Vitals
  observeWebVitals() {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        this.performanceMetrics.lcp = lastEntry.startTime;
        this.sendMetric('web_vitals', {
          metric: 'lcp',
          value: lastEntry.startTime,
          rating: this.getLCPRating(lastEntry.startTime)
        });
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    }

    // First Input Delay (FID)
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.performanceMetrics.fid = entry.processingStart - entry.startTime;
          this.sendMetric('web_vitals', {
            metric: 'fid',
            value: entry.processingStart - entry.startTime,
            rating: this.getFIDRating(entry.processingStart - entry.startTime)
          });
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
    }

    // Cumulative Layout Shift (CLS)
    if ('PerformanceObserver' in window) {
      let clsScore = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (!entry.hadRecentInput) {
            clsScore += entry.value;
          }
        });
        
        this.performanceMetrics.cls = clsScore;
        this.sendMetric('web_vitals', {
          metric: 'cls',
          value: clsScore,
          rating: this.getCLSRating(clsScore)
        });
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  // Track page load performance
  trackPageLoad() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      const metrics = {
        dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
        connection_time: navigation.connectEnd - navigation.connectStart,
        request_time: navigation.responseStart - navigation.requestStart,
        response_time: navigation.responseEnd - navigation.responseStart,
        dom_loading: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        page_load: navigation.loadEventEnd - navigation.navigationStart
      };

      this.sendMetric('page_performance', {
        ...metrics,
        url: window.location.href,
        user_agent: navigator.userAgent,
        connection_type: navigator.connection?.effectiveType || 'unknown'
      });
    });
  }

  // Track resource loading
  trackResourceLoading() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.initiatorType === 'img' || entry.initiatorType === 'xmlhttprequest') {
            this.sendMetric('resource_performance', {
              name: entry.name,
              type: entry.initiatorType,
              duration: entry.duration,
              size: entry.transferSize,
              response_time: entry.responseEnd - entry.responseStart
            });
          }
        });
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
    }
  }

  // Track memory usage
  trackMemoryUsage() {
    if (performance.memory) {
      setInterval(() => {
        this.sendMetric('memory_usage', {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        });
      }, 30000); // Every 30 seconds
    }
  }

  // Initialize user behavior tracking
  initUserBehaviorTracking() {
    // Scroll depth tracking
    this.trackScrollDepth();
    
    // Click tracking
    this.trackClicks();
    
    // Time on page
    this.trackTimeOnPage();
    
    // Exit intent
    this.trackExitIntent();
    
    // Form interactions
    this.trackFormInteractions();
  }

  // Track scroll depth
  trackScrollDepth() {
    let maxScroll = 0;
    const scrollMilestones = [25, 50, 75, 100];
    const reachedMilestones = new Set();

    const handleScroll = () => {
      const scrollPercentage = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      );
      
      maxScroll = Math.max(maxScroll, scrollPercentage);
      
      scrollMilestones.forEach(milestone => {
        if (scrollPercentage >= milestone && !reachedMilestones.has(milestone)) {
          reachedMilestones.add(milestone);
          this.sendEvent('scroll_depth', {
            percentage: milestone,
            url: window.location.href
          });
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Send final scroll depth on page unload
    window.addEventListener('beforeunload', () => {
      this.sendEvent('scroll_depth_final', {
        max_percentage: maxScroll,
        url: window.location.href
      });
    });
  }

  // Track clicks
  trackClicks() {
    document.addEventListener('click', (event) => {
      const target = event.target;
      const tagName = target.tagName.toLowerCase();
      
      // Track specific elements
      if (tagName === 'a' || tagName === 'button' || target.closest('[data-track]')) {
        const elementInfo = {
          tag: tagName,
          text: target.textContent?.trim().substring(0, 100),
          href: target.href,
          class: target.className,
          id: target.id,
          data_track: target.dataset.track,
          position: {
            x: event.clientX,
            y: event.clientY
          }
        };

        this.sendEvent('click', elementInfo);
      }
    });
  }

  // Track time on page
  trackTimeOnPage() {
    let startTime = Date.now();
    let isActive = true;
    
    // Track visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        isActive = false;
        this.sendEvent('page_inactive', {
          duration: Date.now() - startTime,
          url: window.location.href
        });
      } else {
        isActive = true;
        startTime = Date.now();
        this.sendEvent('page_active', {
          url: window.location.href
        });
      }
    });

    // Send time on page before unload
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Date.now() - this.pageStartTime;
      this.sendEvent('time_on_page', {
        duration: timeOnPage,
        url: window.location.href,
        was_active: isActive
      });
    });
  }

  // Track exit intent
  trackExitIntent() {
    let hasTrackedExit = false;
    
    document.addEventListener('mouseleave', (event) => {
      if (event.clientY <= 0 && !hasTrackedExit) {
        hasTrackedExit = true;
        this.sendEvent('exit_intent', {
          url: window.location.href,
          time_on_page: Date.now() - this.pageStartTime
        });
      }
    });
  }

  // Track form interactions
  trackFormInteractions() {
    document.addEventListener('focus', (event) => {
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        this.sendEvent('form_field_focus', {
          field_name: event.target.name,
          field_type: event.target.type,
          form_id: event.target.form?.id,
          url: window.location.href
        });
      }
    });

    document.addEventListener('submit', (event) => {
      const form = event.target;
      const formData = new FormData(form);
      
      this.sendEvent('form_submit', {
        form_id: form.id,
        form_action: form.action,
        field_count: formData.size,
        url: window.location.href
      });
    });
  }

  // Initialize travel-specific tracking
  initTravelTracking() {
    // Track destination interests
    this.trackDestinationInterests();
    
    // Track retreat preferences
    this.trackRetreatPreferences();
    
    // Track booking funnel
    this.trackBookingFunnel();
    
    // Track travel dates
    this.trackTravelDates();
  }

  // Track destination interests
  trackDestinationInterests() {
    const destinationElements = document.querySelectorAll('[data-destination]');
    
    destinationElements.forEach(element => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.sendEvent('destination_view', {
              destination: element.dataset.destination,
              visibility_ratio: entry.intersectionRatio,
              url: window.location.href
            });
          }
        });
      }, { threshold: 0.5 });
      
      observer.observe(element);
    });
  }

  // Track retreat preferences
  trackRetreatPreferences() {
    // Track filter usage
    document.addEventListener('change', (event) => {
      if (event.target.name === 'retreat-filter') {
        this.sendEvent('retreat_filter', {
          filter_type: event.target.dataset.filterType,
          filter_value: event.target.value,
          url: window.location.href
        });
      }
    });

    // Track retreat card interactions
    document.addEventListener('click', (event) => {
      const retreatCard = event.target.closest('[data-retreat-id]');
      if (retreatCard) {
        this.sendEvent('retreat_interest', {
          retreat_id: retreatCard.dataset.retreatId,
          retreat_name: retreatCard.dataset.retreatName,
          destination: retreatCard.dataset.destination,
          price: retreatCard.dataset.price,
          url: window.location.href
        });
      }
    });
  }

  // Track booking funnel
  trackBookingFunnel() {
    const bookingSteps = [
      'booking_initiated',
      'personal_info_completed',
      'accommodation_selected',
      'payment_info_entered',
      'booking_completed'
    ];

    bookingSteps.forEach(step => {
      document.addEventListener(step, (event) => {
        this.sendEvent('booking_funnel', {
          step: step,
          retreat_id: event.detail?.retreatId,
          user_data: event.detail?.userData,
          url: window.location.href
        });
      });
    });
  }

  // Track travel dates
  trackTravelDates() {
    document.addEventListener('change', (event) => {
      if (event.target.type === 'date' && event.target.name.includes('travel')) {
        this.sendEvent('travel_date_selection', {
          date_type: event.target.name,
          selected_date: event.target.value,
          url: window.location.href
        });
      }
    });
  }

  // Rating helpers
  getLCPRating(value) {
    if (value <= 2500) return 'good';
    if (value <= 4000) return 'needs_improvement';
    return 'poor';
  }

  getFIDRating(value) {
    if (value <= 100) return 'good';
    if (value <= 300) return 'needs_improvement';
    return 'poor';
  }

  getCLSRating(value) {
    if (value <= 0.1) return 'good';
    if (value <= 0.25) return 'needs_improvement';
    return 'poor';
  }

  // Send metric to analytics
  sendMetric(type, data) {
    const payload = {
      type,
      data,
      timestamp: Date.now(),
      session_id: this.sessionId,
      user_id: this.userId,
      url: window.location.href,
      referrer: document.referrer,
      user_agent: navigator.userAgent
    };

    this.sendToAnalytics(payload);
  }

  // Send event to analytics
  sendEvent(name, data) {
    const payload = {
      event: name,
      data,
      timestamp: Date.now(),
      session_id: this.sessionId,
      user_id: this.userId,
      url: window.location.href,
      referrer: document.referrer
    };

    this.sendToAnalytics(payload);
  }

  // Send data to analytics service
  sendToAnalytics(payload) {
    // Buffer interactions to reduce API calls
    this.interactionBuffer.push(payload);
    
    // Send buffered data every 5 seconds or when buffer is full
    if (this.interactionBuffer.length >= 10) {
      this.flushBuffer();
    } else if (!this.bufferTimer) {
      this.bufferTimer = setTimeout(() => {
        this.flushBuffer();
      }, 5000);
    }
  }

  // Flush buffer to analytics endpoint
  flushBuffer() {
    if (this.interactionBuffer.length === 0) return;
    
    const data = [...this.interactionBuffer];
    this.interactionBuffer = [];
    
    if (this.bufferTimer) {
      clearTimeout(this.bufferTimer);
      this.bufferTimer = null;
    }

    // Send to multiple analytics services
    Promise.all([
      this.sendToGoogleAnalytics(data),
      this.sendToMixpanel(data),
      this.sendToCustomAnalytics(data)
    ]).catch(error => {
      console.error('Analytics error:', error);
    });
  }

  // Send to Google Analytics
  async sendToGoogleAnalytics(data) {
    if (typeof gtag !== 'undefined') {
      data.forEach(item => {
        if (item.event) {
          gtag('event', item.event, {
            custom_parameter_1: JSON.stringify(item.data),
            session_id: item.session_id,
            user_id: item.user_id
          });
        }
      });
    }
  }

  // Send to Mixpanel
  async sendToMixpanel(data) {
    if (typeof mixpanel !== 'undefined') {
      data.forEach(item => {
        if (item.event) {
          mixpanel.track(item.event, {
            ...item.data,
            session_id: item.session_id,
            user_id: item.user_id,
            timestamp: item.timestamp
          });
        }
      });
    }
  }

  // Send to custom analytics endpoint
  async sendToCustomAnalytics(data) {
    try {
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
    } catch (error) {
      console.error('Custom analytics error:', error);
    }
  }

  // Public methods for manual tracking
  trackRetreatView(retreat) {
    this.sendEvent('retreat_view', {
      retreat_id: retreat.id,
      retreat_name: retreat.name,
      destination: retreat.destination,
      price: retreat.price,
      duration: retreat.duration
    });
  }

  trackBookingStart(retreat) {
    this.sendEvent('booking_start', {
      retreat_id: retreat.id,
      retreat_name: retreat.name,
      destination: retreat.destination,
      price: retreat.price
    });
  }

  trackBookingComplete(booking) {
    this.sendEvent('booking_complete', {
      booking_id: booking.id,
      retreat_id: booking.retreatId,
      amount: booking.amount,
      payment_method: booking.paymentMethod
    });
  }

  trackDestinationGuideView(destination) {
    this.sendEvent('destination_guide_view', {
      destination: destination.name,
      country: destination.country,
      guide_section: destination.section
    });
  }

  trackWeatherCheck(destination) {
    this.sendEvent('weather_check', {
      destination: destination.name,
      country: destination.country
    });
  }

  trackCurrencyConversion(from, to, amount) {
    this.sendEvent('currency_conversion', {
      from_currency: from,
      to_currency: to,
      amount: amount
    });
  }
}

// Create singleton instance
const travelAnalytics = new TravelAnalytics();

// React hook for travel analytics
export const useTravelAnalytics = () => {
  const router = useRouter();
  const isInitialized = useRef(false);

  useEffect(() => {
    if (!isInitialized.current) {
      travelAnalytics.init();
      isInitialized.current = true;
    }
  }, []);

  return {
    trackRetreatView: travelAnalytics.trackRetreatView.bind(travelAnalytics),
    trackBookingStart: travelAnalytics.trackBookingStart.bind(travelAnalytics),
    trackBookingComplete: travelAnalytics.trackBookingComplete.bind(travelAnalytics),
    trackDestinationGuideView: travelAnalytics.trackDestinationGuideView.bind(travelAnalytics),
    trackWeatherCheck: travelAnalytics.trackWeatherCheck.bind(travelAnalytics),
    trackCurrencyConversion: travelAnalytics.trackCurrencyConversion.bind(travelAnalytics)
  };
};

// React component for analytics
export const TravelAnalyticsProvider = ({ children }) => {
  const isInitialized = useRef(false);

  useEffect(() => {
    if (!isInitialized.current) {
      travelAnalytics.init();
      isInitialized.current = true;
    }
  }, []);

  return children;
};

export default travelAnalytics;