'use client';

import { useEffect, Suspense } from 'react';
import <PERSON>ript from 'next/script';
import { usePathname, useSearchParams } from 'next/navigation';

/**
 * 📊 BAKASANA - SEO ANALYTICS COMPONENT
 * 
 * Comprehensive analytics and tracking for SEO optimization:
 * - Google Analytics 4 with enhanced ecommerce
 * - Google Search Console integration
 * - Core Web Vitals monitoring
 * - Custom SEO events tracking
 * - Performance monitoring
 */

const SEOAnalyticsInner = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Google Analytics 4 ID
  const GA4_ID = process.env.NEXT_PUBLIC_GA4_ID || 'G-XXXXXXXXXX';
  const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID || 'GTM-XXXXXXX';
  
  // Track page views
  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
      
      window.gtag('config', GA4_ID, {
        page_location: window.location.href,
        page_title: document.title,
        page_path: url,
        custom_map: {
          custom_parameter_1: 'retreat_interest',
          custom_parameter_2: 'yoga_level'
        }
      });
      
      // Track SEO-specific events
      trackSEOEvent('page_view', {
        page_path: url,
        page_title: document.title,
        page_location: window.location.href
      });
    }
  }, [pathname, searchParams, GA4_ID]);
  
  // Track Core Web Vitals
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Track CLS (Cumulative Layout Shift)
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
            trackSEOEvent('core_web_vitals', {
              metric_name: 'CLS',
              metric_value: entry.value,
              page_path: pathname
            });
          }
        }
      });
      
      try {
        observer.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.warn('Performance Observer not supported');
      }
      
      // Track LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        trackSEOEvent('core_web_vitals', {
          metric_name: 'LCP',
          metric_value: lastEntry.startTime,
          page_path: pathname
        });
      });
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP Observer not supported');
      }
      
      // Track FID (First Input Delay)
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          trackSEOEvent('core_web_vitals', {
            metric_name: 'FID',
            metric_value: entry.processingStart - entry.startTime,
            page_path: pathname
          });
        }
      });
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.warn('FID Observer not supported');
      }
      
      return () => {
        observer.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
      };
    }
  }, [pathname]);
  
  // Track SEO events
  const trackSEOEvent = (eventName, parameters = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'SEO',
        event_label: pathname,
        ...parameters
      });
    }
  };
  
  // Track search queries
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('q') || urlParams.get('search');
    
    if (searchQuery) {
      trackSEOEvent('site_search', {
        search_term: searchQuery,
        page_path: pathname
      });
    }
  }, [pathname]);
  
  // Track retreat interest
  const trackRetreatInterest = (retreatType, action) => {
    trackSEOEvent('retreat_interest', {
      retreat_type: retreatType,
      action: action,
      page_path: pathname
    });
  };
  
  // Track booking funnel
  const trackBookingFunnel = (step, retreatId = null) => {
    trackSEOEvent('booking_funnel', {
      funnel_step: step,
      retreat_id: retreatId,
      page_path: pathname
    });
  };
  
  // Track social sharing
  const trackSocialShare = (platform, url) => {
    trackSEOEvent('social_share', {
      platform: platform,
      shared_url: url,
      page_path: pathname
    });
  };
  
  // Expose tracking functions globally
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.bakasanaTracking = {
        trackRetreatInterest,
        trackBookingFunnel,
        trackSocialShare,
        trackSEOEvent
      };
    }
  }, []);
  
  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `
        }}
      />
      
      {/* Google Analytics 4 */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`}
        strategy="afterInteractive"
      />
      
      <Script
        id="ga4-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            gtag('config', '${GA4_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              send_page_view: false,
              enhanced_measurement: {
                scrolls: true,
                outbound_clicks: true,
                site_search: true,
                video_engagement: true,
                file_downloads: true
              },
              custom_map: {
                'custom_parameter_1': 'retreat_type',
                'custom_parameter_2': 'user_yoga_level',
                'custom_parameter_3': 'booking_stage'
              }
            });
            
            // Enhanced Ecommerce for retreat bookings
            gtag('config', '${GA4_ID}', {
              currency: 'PLN',
              country: 'PL',
              language: 'pl'
            });
          `
        }}
      />
      
      {/* Google Search Console verification */}
      <meta name="google-site-verification" content={process.env.NEXT_PUBLIC_GSC_VERIFICATION || ''} />
      
      {/* Bing Webmaster Tools verification */}
      <meta name="msvalidate.01" content={process.env.NEXT_PUBLIC_BING_VERIFICATION || ''} />
      
      {/* Yandex verification */}
      <meta name="yandex-verification" content={process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || ''} />
      
      {/* Schema.org structured data for analytics */}
      <Script
        id="analytics-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebSite',
            url: 'https://bakasana-travel.blog',
            potentialAction: {
              '@type': 'SearchAction',
              target: {
                '@type': 'EntryPoint',
                urlTemplate: 'https://bakasana-travel.blog/search?q={search_term_string}'
              },
              'query-input': 'required name=search_term_string'
            }
          })
        }}
      />
      
      {/* Performance monitoring */}
      <Script
        id="performance-monitoring"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Monitor page load performance
            window.addEventListener('load', function() {
              setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && window.gtag) {
                  gtag('event', 'page_load_performance', {
                    event_category: 'Performance',
                    page_load_time: Math.round(perfData.loadEventEnd - perfData.fetchStart),
                    dom_content_loaded: Math.round(perfData.domContentLoadedEventEnd - perfData.fetchStart),
                    first_byte: Math.round(perfData.responseStart - perfData.fetchStart)
                  });
                }
              }, 1000);
            });
            
            // Monitor scroll depth
            let maxScroll = 0;
            window.addEventListener('scroll', function() {
              const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
              if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                if (maxScroll % 25 === 0 && window.gtag) {
                  gtag('event', 'scroll_depth', {
                    event_category: 'Engagement',
                    scroll_depth: maxScroll
                  });
                }
              }
            });
          `
        }}
      />
    </>
  );
};

const SEOAnalytics = () => {
  return (
    <Suspense fallback={null}>
      <SEOAnalyticsInner />
    </Suspense>
  );
};

export default SEOAnalytics;
