/**
 * 🌊 BAKASANA - SMOOTH SCROLL PROVIDER
 * 
 * Premium smooth scrolling with:
 * - Lenis smooth scroll implementation
 * - GSAP integration for performance
 * - Scroll progress tracking
 * - Accessibility compliance
 * - Mobile optimization
 * 
 * Features:
 * - Buttery smooth scrolling
 * - Momentum-based easing
 * - Scroll-triggered animations
 * - Performance optimized
 */

'use client';

import React, { useEffect, useRef, createContext, useContext } from 'react';
import { useReducedMotion } from '@/hooks/useAdvancedAnimations';

const SmoothScrollContext = createContext();

export const useSmoothScroll = () => {
  const context = useContext(SmoothScrollContext);
  if (!context) {
    throw new Error('useSmoothScroll must be used within a SmoothScrollProvider');
  }
  return context;
};

const SmoothScrollProvider = ({ children }) => {
  const scrollRef = useRef(null);
  const prefersReducedMotion = useReducedMotion();
  
  useEffect(() => {
    if (prefersReducedMotion) return;
    
    let smoothScroll;
    
    // Custom smooth scroll implementation
    const initSmoothScroll = () => {
      const scrollContainer = document.documentElement;
      let isScrolling = false;
      let currentY = 0;
      let targetY = 0;
      let animationId;
      let lastWheelTime = 0;
      let wheelDelta = 0;
      
      const lerp = (start, end, factor) => {
        return start + (end - start) * factor;
      };
      
      const updateScroll = () => {
        if (!isScrolling) return;
        
        // Bardziej płynne przejście z lepszym easing
        currentY = lerp(currentY, targetY, 0.08);
        
        if (Math.abs(currentY - targetY) < 0.1) {
          currentY = targetY;
          isScrolling = false;
        }
        
        window.scrollTo(0, currentY);
        
        if (isScrolling) {
          animationId = requestAnimationFrame(updateScroll);
        }
      };
      
      const handleWheel = (e) => {
        e.preventDefault();
        
        const now = Date.now();
        const timeDelta = now - lastWheelTime;
        lastWheelTime = now;
        
        // Adaptacyjna prędkość scrollowania
        let scrollSpeed = 1.0;
        
        // Jeśli scrollowanie jest szybkie, zwiększ prędkość
        if (timeDelta < 50) {
          scrollSpeed = 1.8;
        } else if (timeDelta < 100) {
          scrollSpeed = 1.4;
        }
        
        const delta = e.deltaY * scrollSpeed;
        
        targetY = Math.max(0, Math.min(
          document.documentElement.scrollHeight - window.innerHeight,
          targetY + delta
        ));
        
        if (!isScrolling) {
          isScrolling = true;
          updateScroll();
        }
      };
      
      const handleKeydown = (e) => {
        const keyScrollMap = {
          ArrowUp: -50,
          ArrowDown: 50,
          PageUp: -window.innerHeight * 0.8,
          PageDown: window.innerHeight * 0.8,
          Home: -document.documentElement.scrollHeight,
          End: document.documentElement.scrollHeight
        };
        
        if (keyScrollMap[e.key]) {
          e.preventDefault();
          targetY = Math.max(0, Math.min(
            document.documentElement.scrollHeight - window.innerHeight,
            targetY + keyScrollMap[e.key]
          ));
          
          if (!isScrolling) {
            isScrolling = true;
            updateScroll();
          }
        }
      };
      
      // Touch support for mobile
      let touchStartY = 0;
      let touchEndY = 0;
      
      const handleTouchStart = (e) => {
        touchStartY = e.touches[0].clientY;
      };
      
      const handleTouchMove = (e) => {
        e.preventDefault();
        touchEndY = e.touches[0].clientY;
        const delta = touchStartY - touchEndY;
        
        targetY = Math.max(0, Math.min(
          document.documentElement.scrollHeight - window.innerHeight,
          targetY + delta * 2
        ));
        
        touchStartY = touchEndY;
        
        if (!isScrolling) {
          isScrolling = true;
          updateScroll();
        }
      };
      
      // Initialize current position
      currentY = window.pageYOffset;
      targetY = currentY;
      
      // Add event listeners
      document.addEventListener('wheel', handleWheel, { passive: false });
      document.addEventListener('keydown', handleKeydown, { passive: false });
      document.addEventListener('touchstart', handleTouchStart, { passive: false });
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      
      // Cleanup function
      return () => {
        document.removeEventListener('wheel', handleWheel);
        document.removeEventListener('keydown', handleKeydown);
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchmove', handleTouchMove);
        if (animationId) {
          cancelAnimationFrame(animationId);
        }
      };
    };
    
    // Initialize smooth scroll after component mount
    const cleanup = initSmoothScroll();
    
    return cleanup;
  }, [prefersReducedMotion]);
  
  // Scroll to element function
  const scrollToElement = (selector, options = {}) => {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const defaultOptions = {
      duration: 1000,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      offset: 0,
      ...options
    };
    
    const targetPosition = element.offsetTop + defaultOptions.offset;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    const startTime = performance.now();
    
    const easeOutCubic = (t) => {
      return 1 - Math.pow(1 - t, 3);
    };
    
    const animateScroll = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / defaultOptions.duration, 1);
      const easeProgress = easeOutCubic(progress);
      
      window.scrollTo(0, startPosition + distance * easeProgress);
      
      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };
    
    requestAnimationFrame(animateScroll);
  };
  
  // Scroll to top function
  const scrollToTop = (options = {}) => {
    scrollToElement('body', { duration: 800, ...options });
  };
  
  // Get scroll progress
  const getScrollProgress = () => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    return (scrollTop / docHeight) * 100;
  };
  
  const contextValue = {
    scrollToElement,
    scrollToTop,
    getScrollProgress
  };
  
  return (
    <SmoothScrollContext.Provider value={contextValue}>
      {children}
    </SmoothScrollContext.Provider>
  );
};

export default SmoothScrollProvider;

// Scroll Progress Indicator Component
export const ScrollProgressIndicator = ({ 
  className = '',
  color = 'var(--temple-gold)',
  height = '3px',
  position = 'fixed',
  zIndex = 1000
}) => {
  const [progress, setProgress] = React.useState(0);
  const { getScrollProgress } = useSmoothScroll();
  
  React.useEffect(() => {
    const updateProgress = () => {
      setProgress(getScrollProgress());
    };
    
    updateProgress();
    window.addEventListener('scroll', updateProgress, { passive: true });
    
    return () => window.removeEventListener('scroll', updateProgress);
  }, [getScrollProgress]);
  
  return (
    <div
      className={`w-full transition-all duration-300 ${className}`}
      style={{
        position,
        top: 0,
        left: 0,
        height,
        background: 'rgba(42, 39, 36, 0.1)',
        zIndex
      }}
    >
      <div
        className="h-full transition-all duration-300 ease-out"
        style={{
          width: `${progress}%`,
          background: color,
          boxShadow: `0 0 10px ${color}40`
        }}
      />
    </div>
  );
};

// Scroll Trigger Hook
export const useScrollTrigger = (selector, options = {}) => {
  const [isTriggered, setIsTriggered] = React.useState(false);
  
  React.useEffect(() => {
    const element = document.querySelector(selector);
    if (!element) return;
    
    const defaultOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -10% 0px',
      ...options
    };
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsTriggered(entry.isIntersecting);
      },
      defaultOptions
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [selector, options]);
  
  return isTriggered;
};