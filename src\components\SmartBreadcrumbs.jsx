'use client';

import { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NavLink } from '@/components/ui/UnifiedTypography';

const breadcrumbLabels = {
  '': 'Strona główna',
  'retreaty': 'Retreaty',
  'program': 'Program',
  'zajecia-online': 'Zajęcia Online',
  'blog': 'Blog',
  'o-mnie': 'O mnie',
  'kontakt': 'Kontakt',
  'galeria': 'Galeria',
  'rezerwacja': 'Rezerwacja',
  'polityka-prywatnosci': 'Polityka Prywatności',
  'bali': 'Bali',
  'srilanka': 'Sri Lanka',
  'ubud': 'Ubud',
  'canggu': 'Canggu',
  'galle': 'Galle',
  'sigiriya': 'Sigiriya',
  'wellness': 'Wellness',
  'transformacyjne-podroze-azja': 'Transformacyjne Podróże',
  'yoga-retreat-z-polski': 'Yoga Retreat z Polski',
  'retreaty-jogi-bali-2025': 'Retreaty Jogi Bali 2025',
  'joga-sri-lanka-retreat': 'Joga Sri Lanka Retreat',
  'julia-jakubowicz-instruktor': 'Julia Jakubowicz - Instruktor',
  'mapa': 'Mapa',
  'admin': 'Panel Administracyjny',
  'bookings': 'Rezerwacje'
};

export default function SmartBreadcrumbs({ className = '', showHome = true }) {
  const pathname = usePathname();
  
  const breadcrumbs = useMemo(() => {
    // Nie pokazuj breadcrumbs na stronie głównej
    if (pathname === '/') return [];
    
    const pathSegments = pathname.split('/').filter(Boolean);
    const crumbs = [];
    
    // Dodaj stronę główną jeśli showHome = true
    if (showHome) {
      crumbs.push({
        label: breadcrumbLabels[''],
        href: '/',
        isLast: false
      });
    }
    
    // Buduj breadcrumbs z segmentów ścieżki
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      // Dekoduj segment URL
      const decodedSegment = decodeURIComponent(segment);
      
      crumbs.push({
        label: breadcrumbLabels[decodedSegment] || formatSegment(decodedSegment),
        href: currentPath,
        isLast
      });
    });
    
    return crumbs;
  }, [pathname, showHome]);
  
  // Formatuj segment jeśli nie ma etykiety
  const formatSegment = (segment) => {
    return segment
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };
  
  // Nie renderuj jeśli brak breadcrumbs
  if (breadcrumbs.length === 0) return null;
  
  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      {breadcrumbs.map((crumb, index) => (
        <div key={crumb.href} className="flex items-center space-x-2">
          {index > 0 && (
            <svg 
              className="w-3 h-3 text-sage opacity-60" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
            </svg>
          )}
          
          {crumb.isLast ? (
            <span className="text-enterprise-brown font-medium">
              {crumb.label}
            </span>
          ) : (
            <Link 
              href={crumb.href}
              className="group relative"
            >
              <NavLink className="hover:text-enterprise-brown transition-colors duration-300">
                {crumb.label}
              </NavLink>
              <span className="absolute -bottom-0.5 left-0 w-0 h-[1px] bg-enterprise-brown/50 transition-all duration-300 group-hover:w-full"></span>
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
}

// Komponent breadcrumbs dla konkretnych sekcji
export function SectionBreadcrumbs({ section, items = [], className = '' }) {
  const pathname = usePathname();
  
  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Section breadcrumb"
    >
      <Link href="/" className="group relative">
        <NavLink className="hover:text-enterprise-brown transition-colors duration-300">
          Strona główna
        </NavLink>
        <span className="absolute -bottom-0.5 left-0 w-0 h-[1px] bg-enterprise-brown/50 transition-all duration-300 group-hover:w-full"></span>
      </Link>
      
      <svg className="w-3 h-3 text-sage opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
      </svg>
      
      <Link href={section.href} className="group relative">
        <NavLink className="hover:text-enterprise-brown transition-colors duration-300">
          {section.label}
        </NavLink>
        <span className="absolute -bottom-0.5 left-0 w-0 h-[1px] bg-enterprise-brown/50 transition-all duration-300 group-hover:w-full"></span>
      </Link>
      
      {items.map((item, index) => (
        <div key={item.href} className="flex items-center space-x-2">
          <svg className="w-3 h-3 text-sage opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
          </svg>
          
          {item.isLast ? (
            <span className="text-enterprise-brown font-medium">
              {item.label}
            </span>
          ) : (
            <Link href={item.href} className="group relative">
              <NavLink className="hover:text-enterprise-brown transition-colors duration-300">
                {item.label}
              </NavLink>
              <span className="absolute -bottom-0.5 left-0 w-0 h-[1px] bg-enterprise-brown/50 transition-all duration-300 group-hover:w-full"></span>
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
}