'use client';

import Head from 'next/head';
import { usePathname } from 'next/navigation';
import { generateMetaTags, validateMetaTags } from '@/lib/seoManager';
import { 
  generateOrganizationSchema, 
  generateWebPageSchema, 
  generateBreadcrumbSchema,
  DEFAULT_FAQ_DATA,
  generateFAQSchema
} from '@/lib/structuredDataManager';

/**
 * 🚀 ENHANCED SEO HEAD COMPONENT
 * 
 * Comprehensive SEO meta tags and structured data management
 * - Dynamic meta tag generation
 * - Open Graph & Twitter Cards
 * - JSON-LD structured data
 * - Performance optimizations
 * - Accessibility integration
 */

const EnhancedSEOHead = ({ 
  customMeta = {}, 
  breadcrumbs = [],
  structuredData = {},
  children 
}) => {
  const pathname = usePathname();
  
  // Generate meta tags for current page
  const metaTags = generateMetaTags(pathname, customMeta);
  
  // Validate meta tags (development only)
  if (process.env.NODE_ENV === 'development') {
    const validation = validateMetaTags(metaTags);
    if (!validation.isValid) {
      console.warn(`SEO Warnings for ${pathname}:`, validation.warnings);
    }
  }
  
  // Generate structured data
  const organizationSchema = generateOrganizationSchema();
  const webPageSchema = generateWebPageSchema({
    url: pathname,
    title: metaTags.title,
    description: metaTags.description,
    publishDate: customMeta.publishDate || new Date().toISOString(),
    modifiedDate: customMeta.modifiedDate || new Date().toISOString()
  });
  
  const breadcrumbSchema = breadcrumbs.length > 0 ? generateBreadcrumbSchema(breadcrumbs) : null;
  const faqSchema = pathname === '/' ? generateFAQSchema(DEFAULT_FAQ_DATA) : null;
  
  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{metaTags.title}</title>
      <meta name="description" content={metaTags.description} />
      {metaTags.keywords && <meta name="keywords" content={metaTags.keywords} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={metaTags.canonical} />
      
      {/* Language & Locale */}
      <meta httpEquiv="content-language" content={metaTags.language} />
      <meta name="language" content={metaTags.language} />
      
      {/* Robots */}
      <meta name="robots" content={`${metaTags.robots.index ? 'index' : 'noindex'}, ${metaTags.robots.follow ? 'follow' : 'nofollow'}`} />
      <meta name="googlebot" content={`${metaTags.robots.googleBot.index ? 'index' : 'noindex'}, ${metaTags.robots.googleBot.follow ? 'follow' : 'nofollow'}, max-image-preview:large, max-snippet:-1, max-video-preview:-1`} />
      
      {/* Open Graph */}
      <meta property="og:title" content={metaTags.openGraph.title} />
      <meta property="og:description" content={metaTags.openGraph.description} />
      <meta property="og:url" content={metaTags.openGraph.url} />
      <meta property="og:site_name" content={metaTags.openGraph.siteName} />
      <meta property="og:type" content={metaTags.openGraph.type} />
      <meta property="og:locale" content={metaTags.openGraph.locale} />
      {metaTags.openGraph.images.map((image, index) => (
        <React.Fragment key={index}>
          <meta property="og:image" content={image.url} />
          <meta property="og:image:width" content={image.width.toString()} />
          <meta property="og:image:height" content={image.height.toString()} />
          <meta property="og:image:alt" content={image.alt} />
          <meta property="og:image:type" content={image.type} />
        </React.Fragment>
      ))}
      {metaTags.openGraph.appId && <meta property="fb:app_id" content={metaTags.openGraph.appId} />}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={metaTags.twitter.card} />
      <meta name="twitter:site" content={metaTags.twitter.site} />
      <meta name="twitter:creator" content={metaTags.twitter.creator} />
      <meta name="twitter:title" content={metaTags.twitter.title} />
      <meta name="twitter:description" content={metaTags.twitter.description} />
      <meta name="twitter:image" content={metaTags.twitter.images[0]} />
      
      {/* Alternate Languages */}
      {Object.entries(metaTags.alternates.languages).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}
      
      {/* Additional Meta Tags */}
      {Object.entries(metaTags.other).map(([name, content]) => (
        <meta key={name} name={name} content={content} />
      ))}
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="dns-prefetch" href="//images.unsplash.com" />
      <link rel="dns-prefetch" href="//cdn.sanity.io" />
      
      {/* Structured Data - Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      
      {/* Structured Data - WebPage */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webPageSchema)
        }}
      />
      
      {/* Structured Data - Breadcrumbs */}
      {breadcrumbSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbSchema)
          }}
        />
      )}
      
      {/* Structured Data - FAQ */}
      {faqSchema && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(faqSchema)
          }}
        />
      )}
      
      {/* Custom Structured Data */}
      {Object.entries(structuredData).map(([key, schema]) => (
        <script
          key={key}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
      
      {/* Performance Hints */}
      <meta httpEquiv="x-dns-prefetch-control" content="on" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      
      {/* PWA Meta Tags */}
      <meta name="theme-color" content="#FDFCF8" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="BAKASANA" />
      <link rel="manifest" href="/manifest.json" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      
      {/* Favicon */}
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="shortcut icon" href="/favicon.ico" />
      
      {/* Additional custom head content */}
      {children}
    </Head>
  );
};

export default EnhancedSEOHead;

// Hook for easy SEO management in pages
export const useSEO = (seoData) => {
  const pathname = usePathname();
  
  return {
    metaTags: generateMetaTags(pathname, seoData),
    pathname,
    validation: validateMetaTags(generateMetaTags(pathname, seoData))
  };
};
