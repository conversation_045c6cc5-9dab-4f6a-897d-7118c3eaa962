'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Section, SectionContainer } from '@/components/ui/Section';
import TransformationCTA from '../../components/TransformationCTA';



export default function OMniePage() {

  return (
    <main className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>
          
          <h1 className="magazine-title">
            <PERSON>
          </h1>

          <p className="magazine-subtitle">
            Instruktorka jogi RYT 500 • Fizjoterapeutka • Twoja przyjaciółka
          </p>

          <div className="magazine-meta">
            Przewodniczka duchowych przygód i powrotów do siebie
          </div>
          
          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* SEKCJA GŁÓWNA - Dwukolumnowa */}
      <Section spacing="default">
        <SectionContainer>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-xl lg:gap-4xl items-start">
          {/* Kolumna tekstowa (lewa) */}
          <div>
            <h2 className="text-display font-primary font-light text-charcoal mb-xl tracking-tight">
              Cześć, jestem Julia!
            </h2>

            <p className="text-body-lg font-secondary font-light text-sage leading-relaxed mb-lg">
              Prawdziwa transformacja dzieje się nie tylko na macie — ale w codzienności, w sposobie, 
              w jaki oddychamy, poruszamy się i odnosimy do świata. I właśnie w tym chcę Ci towarzyszyć.
            </p>

            <div className="text-body font-secondary font-light text-charcoal leading-loose tracking-wide mb-3xl text-left">
              Bali i Sri Lanka nauczyły mnie słuchania tej prawdziwej ciszy, która mieszka 
              w sercu. Każda podróż to powrót do domu - do tego, kim naprawdę jesteśmy, 
              gdy zdjemy wszystkie maski i pozwolimy sobie być sobą.
            </div>

            <div className="mb-4xl">
              <h3 className="text-heading-lg font-primary font-normal text-charcoal mb-xl">
                Jak pracuję z Tobą
              </h3>
              
              <div className="space-y-sm">
                {[
                  'Łączę wiedzę fizjoterapeuty z duchowością jogi',
                  'Tworzę bezpieczną przestrzeń, gdzie możesz być sobą',
                  'Pomagam Ci zbudować przyjaźń z własnym ciałem'
                ].map((approach, index) => (
                  <div key={index} className="py-sm pl-lg border-l border-enterprise-brown/10 relative">
                    <span className="text-caption font-secondary font-light text-ash">
                      {approach}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Kolumna wizualna (prawa) */}
          <div className="relative h-96 lg:h-[500px] overflow-hidden">
            <Image
              src="/images/profile/omnie-opt.webp"
              alt="Julia Jakubowicz - instruktorka jogi"
              fill
              style={{
                objectFit: 'cover',
                filter: 'grayscale(100%) brightness(1.05)'
              }}
              sizes="(max-width: 768px) 100vw, 45vw"
              quality={95}
            />
          </div>
          </div>
        </SectionContainer>
      </Section>

      {/* SEKCJA DOŚWIADCZENIE - Odwrócona dwukolumnowa */}
      <Section spacing="default" background="whisper">
        <SectionContainer>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-xl lg:gap-4xl items-start">
          {/* Kolumna wizualna (lewa) */}
          <div className="flex items-center justify-center">
            <div className="flex items-center justify-center gap-2 lg:gap-5">
              <div className="w-10 h-10 lg:w-15 lg:h-15 border border-enterprise-brown/15 rounded-full transform translate-x-1 lg:translate-x-2"></div>
              <div className="w-10 h-10 lg:w-15 lg:h-15 border border-enterprise-brown/15 rounded-full transform -translate-x-1 lg:-translate-x-2"></div>
              <div className="w-10 h-10 lg:w-15 lg:h-15 border border-enterprise-brown/15 rounded-full transform -translate-x-4 lg:-translate-x-8"></div>
            </div>
          </div>

          {/* Kolumna tekstowa (prawa) */}
          <div>
            <h2 className="text-display font-primary font-light text-charcoal mb-xl tracking-tight">
              Doświadczenie
            </h2>

            <p className="text-body-lg font-secondary font-light text-sage leading-relaxed mb-lg">
              Ponad 8 lat praktyki i nauczania jogi w różnych formach i miejscach.
            </p>

            <div className="text-body font-secondary font-light text-charcoal leading-loose tracking-wide mb-3xl text-left">
              Od sceptycznej fizjoterapeutki do przewodniczki duchowych transformacji. 
              Każda podróż na Bali i Sri Lanka to lekcja pokory wobec starożytnej mądrości.
            </div>

            <div className="mb-4xl">
              <h3 className="text-heading-lg font-primary font-normal text-charcoal mb-xl">
                Kwalifikacje i certyfikaty
              </h3>
              
              <div>
                {[
                  { name: 'RYT 500 - Certified Yoga Teacher', duration: 'Yoga Alliance', note: 'Registered Yoga Alliance' },
                  { name: '8 lat praktyki nauczania', duration: 'Doświadczenie', note: 'Prowadzenie grup i sesji indywidualnych' },
                  { name: 'Fizjoterapeutka', duration: 'Terapia manualna', note: 'Medyczne podejście do jogi' },
                  { name: '200+ uczniów', duration: 'Azja', note: 'Retreaty na Bali i Sri Lanka' }
                ].map((option, index) => (
                  <div key={index} className={`flex justify-between items-center py-lg ${index !== 3 ? 'border-b border-sage/15' : ''}`}>
                    <div>
                      <h4 className="text-body font-secondary font-normal text-charcoal mb-xs">
                        {option.name}
                      </h4>
                      <p className="text-small font-secondary font-light text-stone m-0">
                        {option.duration}
                      </p>
                    </div>
                    <span className="text-small font-secondary font-light text-stone">
                      {option.note}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          </div>
        </SectionContainer>
      </Section>

      {/* OSTATNIA SEKCJA - KONTAKT */}
      <TransformationCTA />
    </main>
  );
}