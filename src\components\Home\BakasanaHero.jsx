'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const BakasanaHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax Effect */}
      <div 
        className="absolute inset-0 z-0 transform-gpu"
        style={{
          transform: 'translateZ(0)', // Hardware acceleration
        }}
      >
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi Bali i Sri Lanka"
          fill
          className="object-cover object-center scale-105 transition-transform duration-[10s] ease-out"
          priority
          sizes="100vw"
          quality={95}
        />
        
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/20 to-black/40" />
        <div className="absolute inset-0 bg-sanctuary/10" />
      </div>

      {/* Main Content */}
      <div className={`relative z-20 text-center max-w-6xl mx-auto px-6 lg:px-8 transition-all duration-1000 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}>
        <div className="space-y-8">
          
          {/* Small Label */}
          <div className={`inline-flex items-center gap-3 px-8 py-4 bg-sanctuary/90 backdrop-blur-sm border border-temple-gold/20 transition-all duration-700 delay-200 ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
          }`}>
            <span className="w-2 h-2 bg-temple-gold rectangular animate-pulse"></span>
            <span className="text-sm font-secondary text-charcoal tracking-[0.2em] uppercase font-medium">
              RETREAT 2021 • Bali & Sri Lanka
            </span>
          </div>

          {/* Main Title - Old Money Style */}
          <h1 className={`font-primary text-hero font-light text-white leading-none tracking-ultra transition-all duration-entrance delay-500 ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'
          }`}
          style={{
            textShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            BAKASANA
          </h1>

          {/* Subtitle - Old Money Style */}
          <p className={`font-primary font-light text-white tracking-wider transition-all duration-slow delay-1000 text-subtitle mt-xl ${
            isLoaded ? 'opacity-70 translate-y-0' : 'opacity-0 translate-y-5'
          }`}>
            jóga jest drogą ciszy
          </p>

          {/* Description */}
          <p className={`text-body-lg text-sanctuary/80 max-w-3xl mx-auto leading-relaxed font-secondary font-light drop-shadow-sm transition-all duration-normal delay-500 ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
          }`}>
            Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
          </p>

          {/* Statistics */}
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto py-8 transition-all duration-normal delay-600 ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
          }`}>
            <div className="text-center group">
              <div className="text-display font-primary font-medium text-temple-gold mb-2 drop-shadow-md group-hover:scale-110 transition-transform duration-normal">
                24+
              </div>
              <div className="text-caption text-sanctuary/70 font-secondary tracking-wide">
                miejsca
              </div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-cormorant font-medium text-temple-gold mb-2 drop-shadow-md group-hover:scale-110 transition-transform duration-300">
                10
              </div>
              <div className="text-sm md:text-base text-sanctuary/70 font-lato tracking-wide">
                lat doświadczenia
              </div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-cormorant font-medium text-temple-gold mb-2 drop-shadow-md group-hover:scale-110 transition-transform duration-300">
                500+
              </div>
              <div className="text-sm md:text-base text-sanctuary/70 font-lato tracking-wide">
                zadowolonych uczestników
              </div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-cormorant font-medium text-temple-gold mb-2 drop-shadow-md group-hover:scale-110 transition-transform duration-300">
                A+
              </div>
              <div className="text-sm md:text-base text-sanctuary/70 font-lato tracking-wide">
                ocena satysfakcji
              </div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center pt-4 transition-all duration-normal delay-700 ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
          }`}>
            {/* Ghost/Outline Button */}
            <Link
              href="/program"
              className="group inline-flex items-center gap-3 px-10 py-4 bg-transparent text-sanctuary border-2 border-sanctuary/60 font-secondary font-medium tracking-wide transition-all duration-normal hover:bg-sanctuary hover:text-charcoal hover:border-sanctuary hover:shadow-hover backdrop-blur-sm"
            >
              <span>Przegląd harmonogramu</span>
              <svg className="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </Link>
            
            {/* Filled Button */}
            <Link
              href="/rezerwacja"
              className="group inline-flex items-center gap-3 px-10 py-4 bg-temple-gold text-sanctuary font-secondary font-medium tracking-wide transition-all duration-normal hover:bg-enterprise-brown hover:shadow-hover hover:scale-105 backdrop-blur-sm"
            >
              <span>Rezerwuj</span>
              <svg className="w-5 h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </div>

      {/* Floating Decorative Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none overflow-hidden">
        {/* Floating particles */}
        <div 
          className="absolute top-1/4 left-1/4 w-3 h-3 bg-temple-gold/60 rectangular"
          style={{
            animation: 'float 6s ease-in-out infinite'
          }}
        />
        <div 
          className="absolute top-1/3 right-1/3 w-2 h-2 bg-sanctuary/50 rectangular"
          style={{
            animation: 'float 8s ease-in-out infinite',
            animationDelay: '2s'
          }}
        />
        <div 
          className="absolute bottom-1/3 left-1/3 w-2.5 h-2.5 bg-temple-gold/40 rectangular"
          style={{
            animation: 'float 7s ease-in-out infinite',
            animationDelay: '4s'
          }}
        />
        <div 
          className="absolute top-2/3 right-1/4 w-1.5 h-1.5 bg-sanctuary/60 rectangular"
          style={{
            animation: 'float 9s ease-in-out infinite',
            animationDelay: '1s'
          }}
        />
      </div>

      {/* Scroll Indicator */}
      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 transition-all duration-1000 delay-1000 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <div className="flex flex-col items-center text-sanctuary/70">
          <span className="text-xs mb-3 font-lato tracking-[0.2em] uppercase font-light">
            Odkryj więcej
          </span>
          <div className="w-px h-8 bg-sanctuary/30 animate-pulse"></div>
          <svg className="w-5 h-5 mt-2 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>


    </section>
  );
};

export default BakasanaHero;