'use client';

import { useState, useEffect } from 'react';

/**
 * 📊 ANALYTICS DASHBOARD
 * Real-time analytics dashboard for monitoring website performance
 */

export default function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState({
    pageViews: 0,
    sessions: 0,
    bounceRate: 0,
    avgSessionDuration: 0,
    topPages: [],
    retreatInterest: [],
    formSubmissions: 0,
    conversionRate: 0
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching analytics data
    const fetchAnalytics = async () => {
      setIsLoading(true);
      
      // In real implementation, this would fetch from Google Analytics API
      // For now, we'll simulate the data
      setTimeout(() => {
        setAnalytics({
          pageViews: 1247,
          sessions: 892,
          bounceRate: 34.2,
          avgSessionDuration: 185, // seconds
          topPages: [
            { page: '/', views: 456, title: '<PERSON><PERSON> główna' },
            { page: '/retreaty', views: 234, title: 'Retreaty' },
            { page: '/o-mnie', views: 189, title: 'O mnie' },
            { page: '/kontakt', views: 156, title: 'Kontakt' },
            { page: '/blog', views: 98, title: 'Blog' }
          ],
          retreatInterest: [
            { retreat: 'Bali Yoga Retreat - Wiosna 2025', interest: 89 },
            { retreat: 'Sri Lanka Yoga Retreat - Jesień 2025', interest: 67 },
            { retreat: 'Retreat w Tatrach - Jesień 2025', interest: 45 },
            { retreat: 'Bali Yoga Retreat - Maj 2025', interest: 34 }
          ],
          formSubmissions: 23,
          conversionRate: 2.6
        });
        setIsLoading(false);
      }, 1000);
    };

    fetchAnalytics();
  }, []);

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="p-8 bg-white rounded-lg shadow-soft">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-white rounded-lg shadow-soft">
      <h2 className="text-2xl font-serif text-enterprise-brown mb-6">
        📊 Analytics Dashboard
      </h2>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-sanctuary p-6 rounded-lg text-center">
          <div className="text-3xl font-bold text-enterprise-brown">
            {analytics.pageViews.toLocaleString()}
          </div>
          <div className="text-wood-light text-sm">Wyświetlenia stron</div>
        </div>

        <div className="bg-sanctuary p-6 rounded-lg text-center">
          <div className="text-3xl font-bold text-enterprise-brown">
            {analytics.sessions.toLocaleString()}
          </div>
          <div className="text-wood-light text-sm">Sesje</div>
        </div>

        <div className="bg-sanctuary p-6 rounded-lg text-center">
          <div className="text-3xl font-bold text-enterprise-brown">
            {analytics.bounceRate}%
          </div>
          <div className="text-wood-light text-sm">Współczynnik odrzuceń</div>
        </div>

        <div className="bg-sanctuary p-6 rounded-lg text-center">
          <div className="text-3xl font-bold text-enterprise-brown">
            {formatDuration(analytics.avgSessionDuration)}
          </div>
          <div className="text-wood-light text-sm">Średni czas sesji</div>
        </div>
      </div>

      {/* Charts and Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Pages */}
        <div className="bg-sanctuary p-6 rounded-lg">
          <h3 className="text-lg font-serif text-enterprise-brown mb-4">
            📄 Najpopularniejsze strony
          </h3>
          <div className="space-y-3">
            {analytics.topPages.map((page, index) => (
              <div key={index} className="flex justify-between items-center">
                <div>
                  <div className="font-medium text-enterprise-brown">
                    {page.title}
                  </div>
                  <div className="text-sm text-wood-light">{page.page}</div>
                </div>
                <div className="text-lg font-bold text-enterprise-brown">
                  {page.views}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Retreat Interest */}
        <div className="bg-sanctuary p-6 rounded-lg">
          <h3 className="text-lg font-serif text-enterprise-brown mb-4">
            🧘‍♀️ Zainteresowanie retreatami
          </h3>
          <div className="space-y-3">
            {analytics.retreatInterest.map((retreat, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-enterprise-brown">
                    {retreat.retreat}
                  </div>
                  <div className="text-sm font-bold text-enterprise-brown">
                    {retreat.interest}
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-temple-gold h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(retreat.interest / 100) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Conversion Metrics */}
      <div className="mt-8 bg-sanctuary p-6 rounded-lg">
        <h3 className="text-lg font-serif text-enterprise-brown mb-4">
          📈 Konwersje
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-enterprise-brown">
              {analytics.formSubmissions}
            </div>
            <div className="text-wood-light text-sm">Wysłane formularze</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-temple-gold">
              {analytics.conversionRate}%
            </div>
            <div className="text-wood-light text-sm">Współczynnik konwersji</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-enterprise-brown">
              {Math.round(analytics.sessions * (analytics.conversionRate / 100))}
            </div>
            <div className="text-wood-light text-sm">Potencjalni klienci</div>
          </div>
        </div>
      </div>

      {/* Real-time Activity */}
      <div className="mt-8 bg-sanctuary p-6 rounded-lg">
        <h3 className="text-lg font-serif text-enterprise-brown mb-4">
          🔴 Aktywność na żywo
        </h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-wood-light">Aktywni użytkownicy: 12</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-wood-light">Nowe sesje: 3</span>
          </div>
        </div>
      </div>

      <div className="mt-6 text-center text-sm text-wood-light">
        Ostatnia aktualizacja: {new Date().toLocaleString('pl-PL')}
      </div>
    </div>
  );
}
