/**
 * BAKASANA RESPONSIVENESS AUDIT SCRIPT
 * Comprehensive testing across multiple device categories and screen sizes
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Device configurations for testing
const DEVICE_CONFIGS = {
  // Desktop configurations
  desktop: [
    { name: 'Desktop 1920x1080', width: 1920, height: 1080, deviceScaleFactor: 1 },
    { name: 'Desktop 1366x768', width: 1366, height: 768, deviceScaleFactor: 1 },
    { name: 'Desktop 2560x1440', width: 2560, height: 1440, deviceScaleFactor: 1 },
    { name: 'Desktop 1440x900', width: 1440, height: 900, deviceScaleFactor: 1 }
  ],
  
  // Tablet configurations
  tablet: [
    { name: 'iPad Portrait', width: 768, height: 1024, deviceScaleFactor: 2 },
    { name: 'iPad Landscape', width: 1024, height: 768, deviceScaleFactor: 2 },
    { name: 'iPad Pro Portrait', width: 1024, height: 1366, deviceScaleFactor: 2 },
    { name: 'iPad Pro Landscape', width: 1366, height: 1024, deviceScaleFactor: 2 },
    { name: 'Android Tablet Portrait', width: 800, height: 1280, deviceScaleFactor: 1.5 },
    { name: 'Android Tablet Landscape', width: 1280, height: 800, deviceScaleFactor: 1.5 }
  ],
  
  // Mobile configurations
  mobile: [
    { name: 'iPhone SE', width: 375, height: 667, deviceScaleFactor: 2 },
    { name: 'iPhone 12/13/14', width: 390, height: 844, deviceScaleFactor: 3 },
    { name: 'iPhone 14 Plus', width: 428, height: 926, deviceScaleFactor: 3 },
    { name: 'iPhone 14 Pro Max', width: 430, height: 932, deviceScaleFactor: 3 },
    { name: 'Samsung Galaxy S21', width: 360, height: 800, deviceScaleFactor: 3 },
    { name: 'Samsung Galaxy Note', width: 412, height: 915, deviceScaleFactor: 2.625 },
    { name: 'Google Pixel 6', width: 412, height: 915, deviceScaleFactor: 2.625 },
    { name: 'Small Mobile 320px', width: 320, height: 568, deviceScaleFactor: 2 }
  ]
};

// Pages to test
const TEST_PAGES = [
  { url: '/', name: 'Homepage' },
  { url: '/retreaty', name: 'Retreats Page' },
  { url: '/o-mnie', name: 'About Page' },
  { url: '/kontakt', name: 'Contact Page' },
  { url: '/blog', name: 'Blog Page' },
  { url: '/zajecia-online', name: 'Online Classes' }
];

// Elements to check for responsiveness
const RESPONSIVE_CHECKS = {
  navigation: {
    selector: 'nav',
    checks: ['visibility', 'layout', 'touch-targets']
  },
  hero: {
    selector: '.hero, [class*="hero"]',
    checks: ['text-scaling', 'image-scaling', 'layout']
  },
  content: {
    selector: 'main',
    checks: ['text-readability', 'spacing', 'overflow']
  },
  buttons: {
    selector: 'button, a[role="button"], .btn',
    checks: ['touch-targets', 'spacing', 'text-scaling']
  },
  images: {
    selector: 'img',
    checks: ['scaling', 'aspect-ratio', 'loading']
  },
  forms: {
    selector: 'form, input, textarea, select',
    checks: ['touch-targets', 'spacing', 'usability']
  },
  cards: {
    selector: '.card, [class*="card"]',
    checks: ['layout', 'spacing', 'content-fit']
  }
};

class ResponsivenessAuditor {
  constructor() {
    this.browser = null;
    this.results = {
      summary: {},
      devices: {},
      issues: [],
      recommendations: []
    };
  }

  async init() {
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: null,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }

  async testDevice(deviceConfig, pageUrl, pageName) {
    const page = await this.browser.newPage();
    
    try {
      // Set device configuration
      await page.setViewport({
        width: deviceConfig.width,
        height: deviceConfig.height,
        deviceScaleFactor: deviceConfig.deviceScaleFactor
      });

      // Navigate to page
      await page.goto(`http://localhost:3002${pageUrl}`, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Wait for page to fully load
      await new Promise(resolve => setTimeout(resolve, 2000));

      const deviceResults = {
        device: deviceConfig.name,
        page: pageName,
        url: pageUrl,
        viewport: {
          width: deviceConfig.width,
          height: deviceConfig.height,
          deviceScaleFactor: deviceConfig.deviceScaleFactor
        },
        checks: {},
        issues: [],
        screenshot: null
      };

      // Perform responsive checks
      for (const [elementType, config] of Object.entries(RESPONSIVE_CHECKS)) {
        deviceResults.checks[elementType] = await this.checkElement(page, config);
      }

      // Check for horizontal scrolling
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.documentElement.scrollWidth > window.innerWidth;
      });

      if (hasHorizontalScroll) {
        deviceResults.issues.push({
          type: 'horizontal-scroll',
          severity: 'high',
          message: 'Page has horizontal scrolling which should be avoided'
        });
      }

      // Check touch target sizes (mobile only)
      if (deviceConfig.width <= 768) {
        const touchTargetIssues = await this.checkTouchTargets(page);
        deviceResults.issues.push(...touchTargetIssues);
      }

      // Take screenshot
      const screenshotPath = `screenshots/${deviceConfig.name.replace(/\s+/g, '_')}_${pageName.replace(/\s+/g, '_')}.png`;
      await page.screenshot({
        path: screenshotPath,
        fullPage: true
      });
      deviceResults.screenshot = screenshotPath;

      return deviceResults;

    } catch (error) {
      console.error(`Error testing ${deviceConfig.name} on ${pageName}:`, error);
      return {
        device: deviceConfig.name,
        page: pageName,
        error: error.message
      };
    } finally {
      await page.close();
    }
  }

  async checkElement(page, config) {
    try {
      const elements = await page.$$(config.selector);
      
      if (elements.length === 0) {
        return { found: false, message: 'Element not found' };
      }

      const results = {
        found: true,
        count: elements.length,
        checks: {}
      };

      // Check visibility
      if (config.checks.includes('visibility')) {
        results.checks.visibility = await this.checkVisibility(page, elements);
      }

      // Check touch targets
      if (config.checks.includes('touch-targets')) {
        results.checks.touchTargets = await this.checkTouchTargetSizes(page, elements);
      }

      // Check text scaling
      if (config.checks.includes('text-scaling')) {
        results.checks.textScaling = await this.checkTextScaling(page, elements);
      }

      // Check layout
      if (config.checks.includes('layout')) {
        results.checks.layout = await this.checkLayout(page, elements);
      }

      return results;

    } catch (error) {
      return { error: error.message };
    }
  }

  async checkVisibility(page, elements) {
    const visibilityResults = [];
    
    for (const element of elements) {
      const isVisible = await page.evaluate(el => {
        const rect = el.getBoundingClientRect();
        const style = window.getComputedStyle(el);
        return rect.width > 0 && rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none';
      }, element);
      
      visibilityResults.push(isVisible);
    }
    
    return {
      visible: visibilityResults.filter(v => v).length,
      hidden: visibilityResults.filter(v => !v).length,
      total: visibilityResults.length
    };
  }

  async checkTouchTargetSizes(page, elements) {
    const touchTargetResults = [];
    
    for (const element of elements) {
      const size = await page.evaluate(el => {
        const rect = el.getBoundingClientRect();
        return { width: rect.width, height: rect.height };
      }, element);
      
      const meetsMinimum = size.width >= 44 && size.height >= 44;
      touchTargetResults.push({ size, meetsMinimum });
    }
    
    return {
      total: touchTargetResults.length,
      meetingMinimum: touchTargetResults.filter(r => r.meetsMinimum).length,
      belowMinimum: touchTargetResults.filter(r => !r.meetsMinimum).length,
      details: touchTargetResults
    };
  }

  async checkTextScaling(page, elements) {
    const textResults = [];
    
    for (const element of elements) {
      const textInfo = await page.evaluate(el => {
        const style = window.getComputedStyle(el);
        return {
          fontSize: parseFloat(style.fontSize),
          lineHeight: style.lineHeight,
          textOverflow: style.textOverflow,
          whiteSpace: style.whiteSpace
        };
      }, element);
      
      textResults.push(textInfo);
    }
    
    return textResults;
  }

  async checkLayout(page, elements) {
    const layoutResults = [];
    
    for (const element of elements) {
      const layoutInfo = await page.evaluate(el => {
        const rect = el.getBoundingClientRect();
        const style = window.getComputedStyle(el);
        return {
          position: { x: rect.x, y: rect.y },
          size: { width: rect.width, height: rect.height },
          overflow: style.overflow,
          display: style.display,
          flexDirection: style.flexDirection,
          gridTemplateColumns: style.gridTemplateColumns
        };
      }, element);
      
      layoutResults.push(layoutInfo);
    }
    
    return layoutResults;
  }

  async checkTouchTargets(page) {
    const issues = [];
    
    const touchElements = await page.$$('button, a, input[type="button"], input[type="submit"], [role="button"]');
    
    for (const element of touchElements) {
      const size = await page.evaluate(el => {
        const rect = el.getBoundingClientRect();
        return { width: rect.width, height: rect.height };
      }, element);
      
      if (size.width < 44 || size.height < 44) {
        issues.push({
          type: 'touch-target-too-small',
          severity: 'medium',
          message: `Touch target is ${size.width}x${size.height}px, should be at least 44x44px`,
          element: await page.evaluate(el => el.tagName + (el.className ? '.' + el.className : ''), element)
        });
      }
    }
    
    return issues;
  }

  async runFullAudit() {
    console.log('🚀 Starting comprehensive responsiveness audit...');
    
    // Create screenshots directory
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }

    const allDevices = [
      ...DEVICE_CONFIGS.desktop,
      ...DEVICE_CONFIGS.tablet,
      ...DEVICE_CONFIGS.mobile
    ];

    for (const page of TEST_PAGES) {
      console.log(`\n📄 Testing page: ${page.name} (${page.url})`);
      
      for (const device of allDevices) {
        console.log(`  📱 Testing on: ${device.name}`);
        
        const result = await this.testDevice(device, page.url, page.name);
        
        if (!this.results.devices[page.name]) {
          this.results.devices[page.name] = [];
        }
        
        this.results.devices[page.name].push(result);
        
        // Collect issues
        if (result.issues) {
          this.results.issues.push(...result.issues.map(issue => ({
            ...issue,
            page: page.name,
            device: device.name
          })));
        }
      }
    }

    // Generate summary and recommendations
    this.generateSummary();
    this.generateRecommendations();
    
    // Save results
    this.saveResults();
    
    console.log('\n✅ Responsiveness audit completed!');
    console.log(`📊 Results saved to: responsiveness-audit-results.json`);
    console.log(`📸 Screenshots saved to: screenshots/`);
  }

  generateSummary() {
    const totalTests = Object.values(this.results.devices).reduce((sum, devices) => sum + devices.length, 0);
    const totalIssues = this.results.issues.length;
    const criticalIssues = this.results.issues.filter(i => i.severity === 'high').length;
    
    this.results.summary = {
      totalTests,
      totalIssues,
      criticalIssues,
      passRate: ((totalTests - totalIssues) / totalTests * 100).toFixed(2) + '%',
      deviceCategories: {
        desktop: DEVICE_CONFIGS.desktop.length,
        tablet: DEVICE_CONFIGS.tablet.length,
        mobile: DEVICE_CONFIGS.mobile.length
      },
      pagesTestedCount: TEST_PAGES.length,
      pagesTested: TEST_PAGES.map(p => p.name)
    };
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Check for common issues
    const horizontalScrollIssues = this.results.issues.filter(i => i.type === 'horizontal-scroll');
    if (horizontalScrollIssues.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'layout',
        issue: 'Horizontal scrolling detected',
        recommendation: 'Review CSS for fixed widths, large margins, or content that exceeds viewport width. Use responsive units and max-width properties.',
        affectedDevices: horizontalScrollIssues.map(i => i.device)
      });
    }
    
    const touchTargetIssues = this.results.issues.filter(i => i.type === 'touch-target-too-small');
    if (touchTargetIssues.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'usability',
        issue: 'Touch targets below minimum size',
        recommendation: 'Increase button and link sizes to at least 44x44px for better mobile usability. Add padding or increase font sizes.',
        affectedElements: touchTargetIssues.length
      });
    }
    
    this.results.recommendations = recommendations;
  }

  saveResults() {
    const resultsPath = 'responsiveness-audit-results.json';
    fs.writeFileSync(resultsPath, JSON.stringify(this.results, null, 2));
    
    // Also create a readable HTML report
    this.generateHTMLReport();
  }

  generateHTMLReport() {
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BAKASANA Responsiveness Audit Report</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary-card h3 { color: #2c3e50; margin-bottom: 10px; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #3498db; }
        .issues { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .issue { padding: 15px; margin: 10px 0; border-left: 4px solid #e74c3c; background: #fdf2f2; border-radius: 4px; }
        .issue.medium { border-left-color: #f39c12; background: #fef9e7; }
        .issue.low { border-left-color: #27ae60; background: #eafaf1; }
        .recommendations { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .recommendation { padding: 15px; margin: 10px 0; border-left: 4px solid #3498db; background: #f8f9fa; border-radius: 4px; }
        .device-results { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .device-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .device-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .device-card h4 { color: #2c3e50; margin-bottom: 10px; }
        .status-good { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }
        .screenshot { max-width: 100%; height: auto; border-radius: 4px; margin-top: 10px; }
        .tabs { display: flex; border-bottom: 1px solid #ddd; margin-bottom: 20px; }
        .tab { padding: 10px 20px; cursor: pointer; border: none; background: none; }
        .tab.active { background: #3498db; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 BAKASANA Responsiveness Audit Report</h1>
            <p>Comprehensive analysis across ${Object.values(this.results.devices).reduce((sum, devices) => sum + devices.length, 0)} device configurations</p>
            <p>Generated: ${new Date().toLocaleString()}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="number">${this.results.summary.totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>Issues Found</h3>
                <div class="number">${this.results.summary.totalIssues}</div>
            </div>
            <div class="summary-card">
                <h3>Critical Issues</h3>
                <div class="number">${this.results.summary.criticalIssues}</div>
            </div>
            <div class="summary-card">
                <h3>Pass Rate</h3>
                <div class="number">${this.results.summary.passRate}</div>
            </div>
        </div>

        <div class="issues">
            <h2>🚨 Issues Found</h2>
            ${this.results.issues.map(issue => `
                <div class="issue ${issue.severity}">
                    <strong>${issue.type.replace(/-/g, ' ').toUpperCase()}</strong> - ${issue.device} - ${issue.page}
                    <p>${issue.message}</p>
                </div>
            `).join('')}
        </div>

        <div class="recommendations">
            <h2>💡 Recommendations</h2>
            ${this.results.recommendations.map(rec => `
                <div class="recommendation">
                    <strong>Priority: ${rec.priority.toUpperCase()}</strong> - ${rec.category}
                    <h4>${rec.issue}</h4>
                    <p>${rec.recommendation}</p>
                </div>
            `).join('')}
        </div>

        <div class="device-results">
            <h2>📱 Device Test Results</h2>
            <div class="tabs">
                ${Object.keys(this.results.devices).map((page, index) => `
                    <button class="tab ${index === 0 ? 'active' : ''}" onclick="showTab('${page}')">${page}</button>
                `).join('')}
            </div>

            ${Object.entries(this.results.devices).map(([page, devices], index) => `
                <div id="${page}" class="tab-content ${index === 0 ? 'active' : ''}">
                    <div class="device-grid">
                        ${devices.map(device => `
                            <div class="device-card">
                                <h4>${device.device}</h4>
                                <p><strong>Viewport:</strong> ${device.viewport.width}x${device.viewport.height}</p>
                                <p><strong>Scale:</strong> ${device.viewport.deviceScaleFactor}x</p>
                                <p><strong>Issues:</strong> <span class="${device.issues?.length > 0 ? 'status-error' : 'status-good'}">${device.issues?.length || 0}</span></p>
                                ${device.screenshot ? `<img src="${device.screenshot}" alt="Screenshot" class="screenshot">` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }
    </script>
</body>
</html>`;

    fs.writeFileSync('responsiveness-audit-report.html', htmlContent);
    console.log('📝 HTML report generated: responsiveness-audit-report.html');
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Main execution
async function main() {
  const auditor = new ResponsivenessAuditor();
  
  try {
    await auditor.init();
    await auditor.runFullAudit();
  } catch (error) {
    console.error('❌ Audit failed:', error);
  } finally {
    await auditor.close();
  }
}

// Export for use as module or run directly
if (require.main === module) {
  main();
}

module.exports = ResponsivenessAuditor;
