'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { generateBreadcrumbSchema } from '@/lib/structuredDataManager';
import { SEO_CONFIG } from '@/lib/seoManager';

/**
 * 🍞 SEO OPTIMIZED BREADCRUMBS COMPONENT
 * 
 * Comprehensive breadcrumb navigation with SEO optimization:
 * - Structured data (BreadcrumbList schema)
 * - Semantic HTML with proper ARIA
 * - Accessibility support
 * - Dynamic breadcrumb generation
 * - Keyword-optimized anchor text
 */

const SEOBreadcrumbs = ({ 
  customBreadcrumbs = null, 
  className = '',
  showHome = true,
  separator = '/',
  maxItems = 5 
}) => {
  const pathname = usePathname();
  
  // Generate breadcrumbs from pathname or use custom ones
  const generateBreadcrumbs = () => {
    if (customBreadcrumbs) return customBreadcrumbs;
    
    const pathSegments = pathname.split('/').filter(segment => segment !== '');
    const breadcrumbs = [];
    
    // Always include home if showHome is true
    if (showHome) {
      breadcrumbs.push({
        name: 'Strona Główna',
        url: '/',
        keywords: ['bakasana', 'yoga retreats', 'strona główna']
      });
    }
    
    // Generate breadcrumbs based on path segments
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      const breadcrumbData = getBreadcrumbData(segment, currentPath, index === pathSegments.length - 1);
      if (breadcrumbData) {
        breadcrumbs.push(breadcrumbData);
      }
    });
    
    // Limit breadcrumbs to maxItems
    if (breadcrumbs.length > maxItems) {
      return [
        breadcrumbs[0], // Home
        { name: '...', url: null, isEllipsis: true },
        ...breadcrumbs.slice(-2) // Last 2 items
      ];
    }
    
    return breadcrumbs;
  };
  
  // Get breadcrumb data for specific segments
  const getBreadcrumbData = (segment, path, isLast) => {
    const breadcrumbMap = {
      'retreaty': {
        name: 'Retreaty Jogi',
        url: '/retreaty',
        keywords: ['retreaty jogi', 'yoga retreats', 'wyjazdy']
      },
      'retreaty-jogi-bali-2025': {
        name: 'Retreat Jogi Bali 2025',
        url: '/retreaty-jogi-bali-2025',
        keywords: ['bali retreat', 'yoga bali', 'retreat 2025']
      },
      'joga-sri-lanka-retreat': {
        name: 'Retreat Jogi Sri Lanka',
        url: '/joga-sri-lanka-retreat',
        keywords: ['sri lanka retreat', 'yoga sri lanka', 'sigiriya']
      },
      'o-mnie': {
        name: 'O Mnie',
        url: '/o-mnie',
        keywords: ['julia jakubowicz', 'instruktorka jogi', 'o mnie']
      },
      'julia-jakubowicz-instruktor': {
        name: 'Julia Jakubowicz - Instruktorka',
        url: '/julia-jakubowicz-instruktor',
        keywords: ['julia jakubowicz', 'instruktorka jogi', 'fizjoterapeutka']
      },
      'zajecia-online': {
        name: 'Zajęcia Online',
        url: '/zajecia-online',
        keywords: ['zajęcia jogi online', 'yoga online', 'live classes']
      },
      'galeria': {
        name: 'Galeria',
        url: '/galeria',
        keywords: ['galeria', 'zdjęcia', 'yoga photos']
      },
      'kontakt': {
        name: 'Kontakt',
        url: '/kontakt',
        keywords: ['kontakt', 'rezerwacje', 'informacje']
      },
      'blog': {
        name: 'Blog',
        url: '/blog',
        keywords: ['blog', 'artykuły', 'yoga tips']
      },
      'program': {
        name: 'Program',
        url: '/program',
        keywords: ['program retreatu', 'harmonogram', 'plan']
      },
      'rezerwacja': {
        name: 'Rezerwacja',
        url: '/rezerwacja',
        keywords: ['rezerwacja', 'booking', 'zapisz się']
      },
      'wellness': {
        name: 'Wellness',
        url: '/wellness',
        keywords: ['wellness', 'spa', 'relaks']
      },
      'transformacyjne-podroze-azja': {
        name: 'Transformacyjne Podróże Azja',
        url: '/transformacyjne-podroze-azja',
        keywords: ['transformacyjne podróże', 'azja', 'spiritual journey']
      },
      'yoga-retreat-z-polski': {
        name: 'Yoga Retreat z Polski',
        url: '/yoga-retreat-z-polski',
        keywords: ['yoga retreat z polski', 'wyjazdy z polski', 'grupa polska']
      }
    };
    
    // Check if we have predefined data for this segment
    if (breadcrumbMap[segment]) {
      return breadcrumbMap[segment];
    }
    
    // Generate breadcrumb for blog posts
    if (path.includes('/blog/') && segment !== 'blog') {
      return {
        name: formatBlogTitle(segment),
        url: isLast ? null : path, // Don't link to current page
        keywords: ['blog', 'artykuł', 'yoga']
      };
    }
    
    // Generate breadcrumb for category pages
    if (path.includes('/kategoria/')) {
      return {
        name: `Kategoria: ${formatCategoryName(segment)}`,
        url: isLast ? null : path,
        keywords: ['kategoria', segment, 'blog']
      };
    }
    
    // Default breadcrumb generation
    return {
      name: formatSegmentName(segment),
      url: isLast ? null : path,
      keywords: [segment]
    };
  };
  
  // Format blog title from slug
  const formatBlogTitle = (slug) => {
    return slug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  // Format category name
  const formatCategoryName = (category) => {
    const categoryMap = {
      'bali': 'Bali',
      'sri-lanka': 'Sri Lanka',
      'joga': 'Joga',
      'medytacja': 'Medytacja',
      'ayurveda': 'Ayurveda',
      'wellness': 'Wellness'
    };
    return categoryMap[category] || formatSegmentName(category);
  };
  
  // Format segment name
  const formatSegmentName = (segment) => {
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  const breadcrumbs = generateBreadcrumbs();
  
  // Generate structured data
  const breadcrumbSchema = generateBreadcrumbSchema(
    breadcrumbs.filter(crumb => !crumb.isEllipsis && crumb.url)
  );
  
  if (breadcrumbs.length <= 1) return null;
  
  return (
    <>
      {/* Breadcrumb Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema)
        }}
      />
      
      {/* Breadcrumb Navigation */}
      <nav 
        aria-label="Breadcrumb navigation"
        className={`breadcrumbs ${className}`}
        role="navigation"
      >
        <ol 
          className="flex items-center space-x-2 text-sm text-stone-600"
          itemScope 
          itemType="https://schema.org/BreadcrumbList"
        >
          {breadcrumbs.map((crumb, index) => (
            <li 
              key={index}
              className="flex items-center"
              itemProp="itemListElement"
              itemScope
              itemType="https://schema.org/ListItem"
            >
              {/* Separator */}
              {index > 0 && (
                <span 
                  className="mx-2 text-stone-400" 
                  aria-hidden="true"
                >
                  {separator}
                </span>
              )}
              
              {/* Breadcrumb Item */}
              {crumb.isEllipsis ? (
                <span className="text-stone-400">...</span>
              ) : crumb.url ? (
                <Link
                  href={crumb.url}
                  className="hover:text-temple-gold transition-colors focus:outline-none focus:ring-2 focus:ring-temple-gold focus:ring-offset-2 rounded"
                  itemProp="item"
                  title={`Przejdź do: ${crumb.name}`}
                >
                  <span itemProp="name">{crumb.name}</span>
                  <meta itemProp="position" content={(index + 1).toString()} />
                </Link>
              ) : (
                <span 
                  className="text-charcoal font-medium"
                  itemProp="name"
                  aria-current="page"
                >
                  {crumb.name}
                  <meta itemProp="position" content={(index + 1).toString()} />
                </span>
              )}
              
              {/* Hidden keywords for SEO */}
              {crumb.keywords && (
                <span className="sr-only">
                  Keywords: {crumb.keywords.join(', ')}
                </span>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
};

export default SEOBreadcrumbs;
