/**
 * 🚀 BAKASANA - ADVANCED SEO MANAGER
 * 
 * Comprehensive SEO optimization system following modern best practices:
 * - Dynamic meta tag generation
 * - Open Graph & Twitter Cards
 * - Structured data (Schema.org)
 * - Performance optimization
 * - International SEO support
 */

// SEO Configuration
export const SEO_CONFIG = {
  siteName: 'BAKASANA',
  siteUrl: 'https://bakasana-travel.blog',
  defaultTitle: 'BAKASANA - Luksusowe Retreaty Jogi Bali & Sri Lanka 2025',
  defaultDescription: '🏆 Ekskluzywne wyjazdy wellness Azja z certyfikowaną instruktorką Julią Jakubowicz. ✈️ Premium yoga retreats spiritual journey 2025 | Luksusowe retreaty Ubud, Gili Air, Sigiriya | 4.9/5 ⭐ 127 opinii',
  defaultImage: '/images/og/bakasana-og-homepage-2025.jpg',
  defaultImageAlt: 'BAKASANA - Retreaty jogi na Bali i Sri Lanka 2025',
  twitterHandle: '@bakasana_yoga',
  facebookAppId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  language: 'pl',
  locale: 'pl_PL',
  alternateLocales: ['en_US'],
  organization: {
    name: 'BAKASANA',
    legalName: 'BAKASANA Sp. z o.o.',
    url: 'https://bakasana-travel.blog',
    logo: 'https://bakasana-travel.blog/images/logo/bakasana-logo.png',
    email: '<EMAIL>',
    phone: '+48606101523',
    address: {
      streetAddress: 'ul. Przykładowa 123',
      addressLocality: 'Warszawa',
      addressRegion: 'Mazowieckie',
      postalCode: '00-001',
      addressCountry: 'PL'
    },
    socialMedia: {
      instagram: 'https://www.instagram.com/fly_with_bakasana/',
      facebook: 'https://www.facebook.com/bakasana.yoga',
      youtube: 'https://www.youtube.com/@bakasana-yoga',
      tiktok: 'https://www.tiktok.com/@bakasana.yoga'
    }
  }
};

// Page-specific SEO configurations
export const PAGE_SEO_CONFIG = {
  '/': {
    title: 'BAKASANA - Luksusowe Retreaty Jogi Bali & Sri Lanka 2025 | Premium Yoga Retreats ⭐',
    description: '🏆 Ekskluzywne wyjazdy wellness Azja z certyfikowaną instruktorką Julią Jakubowicz. ✈️ Premium yoga retreats spiritual journey 2025 | Luksusowe retreaty Ubud, Gili Air, Sigiriya | 4.9/5 ⭐ 127 opinii | Rezerwuj teraz!',
    keywords: 'luksusowe retreaty jogi bali sri lanka, ekskluzywne wyjazdy wellness azja, premium yoga retreats spiritual journey, retreaty jogi bali 2025',
    type: 'website',
    image: '/images/og/bakasana-og-homepage-2025.jpg'
  },
  '/retreaty': {
    title: 'Retreaty Jogi 2025 - Bali & Sri Lanka | BAKASANA Premium Yoga',
    description: '🌴 Odkryj nasze ekskluzywne retreaty jogi na Bali i Sri Lanka 2025. Ubud, Gili Air, Sigiriya - transformacyjne podróże z certyfikowaną instruktorką. Rezerwuj już dziś!',
    keywords: 'retreaty jogi 2025, bali yoga retreat, sri lanka joga, ubud retreat, gili air yoga',
    type: 'website',
    image: '/images/og/bakasana-retreaty-2025.jpg'
  },
  '/retreaty-jogi-bali-2025': {
    title: 'Retreat Jogi Bali 2025 - Ubud & Gili Air | BAKASANA Premium',
    description: '🏝️ Luksusowy retreat jogi na Bali 2025 - Ubud & Gili Air. 7-14 dni transformacji z daily joga, medytacją, ayurveda masażami. Certyfikowana instruktorka Julia Jakubowicz.',
    keywords: 'retreat jogi bali 2025, ubud yoga retreat, gili air joga, bali wellness wyjazd',
    type: 'product',
    image: '/images/og/bakasana-bali-retreat-2025.jpg'
  },
  '/joga-sri-lanka-retreat': {
    title: 'Retreat Jogi Sri Lanka 2025 - Sigiriya & Kandy | BAKASANA',
    description: '🐘 Duchowy retreat jogi Sri Lanka 2025 - Sigiriya & Kandy. 10 dni z daily joga, medytacją, ayurveda, zwiedzaniem świątyń. Premium wellness podróż z Julią Jakubowicz.',
    keywords: 'retreat jogi sri lanka 2025, sigiriya yoga retreat, kandy joga, sri lanka wellness',
    type: 'product',
    image: '/images/og/bakasana-srilanka-retreat-2025.jpg'
  },
  '/o-mnie': {
    title: 'Julia Jakubowicz - Instruktorka Jogi & Fizjoterapeutka | BAKASANA',
    description: '👩‍⚕️ Poznaj Julię Jakubowicz - certyfikowaną instruktorkę jogi 200h YTT i fizjoterapeutkę. Specjalistka od retreatów na Bali i Sri Lanka. Doświadczenie, pasja, profesjonalizm.',
    keywords: 'julia jakubowicz joga, instruktorka jogi warszawa, fizjoterapeutka joga, 200h ytt',
    type: 'profile',
    image: '/images/og/julia-jakubowicz-profile.jpg'
  },
  '/zajecia-online': {
    title: 'Zajęcia Jogi Online - Live & On-Demand | BAKASANA',
    description: '💻 Zajęcia jogi online z Julią Jakubowicz. Live sesje, nagrania on-demand, personalne konsultacje. Joga z domu na najwyższym poziomie. Dołącz już dziś!',
    keywords: 'zajęcia jogi online, joga z domu, live yoga classes, julia jakubowicz online',
    type: 'service',
    image: '/images/og/bakasana-online-classes.jpg'
  },
  '/kontakt': {
    title: 'Kontakt - BAKASANA Retreaty Jogi | Rezerwacje & Informacje',
    description: '📞 Skontaktuj się z BAKASANA. Rezerwacje retreatów, informacje o wyjazach, konsultacje. WhatsApp: +48 606 101 523 | Email: <EMAIL>',
    keywords: 'kontakt bakasana, rezerwacje retreaty jogi, julia jakubowicz kontakt',
    type: 'website',
    image: '/images/og/bakasana-contact.jpg'
  }
};

/**
 * Generate comprehensive meta tags for a page
 */
export function generateMetaTags(pathname, customMeta = {}) {
  const pageConfig = PAGE_SEO_CONFIG[pathname] || {};
  const config = { ...pageConfig, ...customMeta };
  
  const title = config.title || SEO_CONFIG.defaultTitle;
  const description = config.description || SEO_CONFIG.defaultDescription;
  const image = config.image || SEO_CONFIG.defaultImage;
  const url = `${SEO_CONFIG.siteUrl}${pathname}`;
  const type = config.type || 'website';
  
  return {
    // Basic Meta Tags
    title,
    description,
    keywords: config.keywords,
    
    // Canonical URL
    canonical: url,
    
    // Language & Locale
    language: SEO_CONFIG.language,
    
    // Open Graph
    openGraph: {
      title,
      description,
      url,
      siteName: SEO_CONFIG.siteName,
      images: [
        {
          url: `${SEO_CONFIG.siteUrl}${image}`,
          width: 1200,
          height: 630,
          alt: config.imageAlt || SEO_CONFIG.defaultImageAlt,
          type: 'image/jpeg'
        }
      ],
      locale: SEO_CONFIG.locale,
      type,
      ...(SEO_CONFIG.facebookAppId && { appId: SEO_CONFIG.facebookAppId })
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      site: SEO_CONFIG.twitterHandle,
      creator: SEO_CONFIG.twitterHandle,
      title: title.length > 70 ? title.substring(0, 67) + '...' : title,
      description: description.length > 200 ? description.substring(0, 197) + '...' : description,
      images: [`${SEO_CONFIG.siteUrl}${image}`]
    },
    
    // Additional Meta
    robots: {
      index: config.noindex !== true,
      follow: config.nofollow !== true,
      nocache: false,
      googleBot: {
        index: config.noindex !== true,
        follow: config.nofollow !== true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // Alternate Languages
    alternates: {
      canonical: url,
      languages: {
        'pl': url,
        'en': `${url}?lang=en`,
      },
    },
    
    // Additional Properties
    other: {
      'theme-color': '#FDFCF8',
      'msapplication-TileColor': '#C9A575',
      'apple-mobile-web-app-title': SEO_CONFIG.siteName,
      'application-name': SEO_CONFIG.siteName,
      'format-detection': 'telephone=no',
    }
  };
}

/**
 * Generate JSON-LD structured data
 */
export function generateStructuredData(type, data = {}) {
  const baseUrl = SEO_CONFIG.siteUrl;
  
  const schemas = {
    Organization: {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: SEO_CONFIG.organization.name,
      legalName: SEO_CONFIG.organization.legalName,
      url: SEO_CONFIG.organization.url,
      logo: SEO_CONFIG.organization.logo,
      email: SEO_CONFIG.organization.email,
      telephone: SEO_CONFIG.organization.phone,
      address: {
        '@type': 'PostalAddress',
        ...SEO_CONFIG.organization.address
      },
      sameAs: Object.values(SEO_CONFIG.organization.socialMedia),
      ...data
    },
    
    LocalBusiness: {
      '@context': 'https://schema.org',
      '@type': 'LocalBusiness',
      '@id': `${baseUrl}/#organization`,
      name: SEO_CONFIG.organization.name,
      image: SEO_CONFIG.organization.logo,
      telephone: SEO_CONFIG.organization.phone,
      email: SEO_CONFIG.organization.email,
      url: SEO_CONFIG.organization.url,
      address: {
        '@type': 'PostalAddress',
        ...SEO_CONFIG.organization.address
      },
      geo: {
        '@type': 'GeoCoordinates',
        latitude: 52.2297,
        longitude: 21.0122
      },
      openingHoursSpecification: {
        '@type': 'OpeningHoursSpecification',
        dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
        opens: '08:00',
        closes: '22:00'
      },
      priceRange: '2900-4200 PLN',
      currenciesAccepted: 'PLN',
      paymentAccepted: ['Bank Transfer', 'BLIK', 'Credit Card'],
      ...data
    },
    
    WebSite: {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      '@id': `${baseUrl}/#website`,
      url: baseUrl,
      name: SEO_CONFIG.siteName,
      description: SEO_CONFIG.defaultDescription,
      publisher: {
        '@id': `${baseUrl}/#organization`
      },
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${baseUrl}/search?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      },
      ...data
    }
  };
  
  return schemas[type] || {};
}

/**
 * Validate and optimize meta tags
 */
export function validateMetaTags(metaTags) {
  const warnings = [];
  
  // Title validation
  if (!metaTags.title) {
    warnings.push('Missing title tag');
  } else if (metaTags.title.length < 30) {
    warnings.push('Title too short (recommended: 50-60 characters)');
  } else if (metaTags.title.length > 60) {
    warnings.push('Title too long (recommended: 50-60 characters)');
  }
  
  // Description validation
  if (!metaTags.description) {
    warnings.push('Missing meta description');
  } else if (metaTags.description.length < 120) {
    warnings.push('Description too short (recommended: 150-160 characters)');
  } else if (metaTags.description.length > 160) {
    warnings.push('Description too long (recommended: 150-160 characters)');
  }
  
  // Image validation
  if (!metaTags.openGraph?.images?.[0]?.url) {
    warnings.push('Missing Open Graph image');
  }
  
  return {
    isValid: warnings.length === 0,
    warnings,
    score: Math.max(0, 100 - (warnings.length * 20))
  };
}
