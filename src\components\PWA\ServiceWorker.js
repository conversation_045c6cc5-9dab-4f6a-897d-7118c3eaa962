// Service Worker for Bakasana Travel Blog PWA
// Enterprise-level caching strategies for optimal performance

const CACHE_NAME = 'bakasana-travel-v1.2.0';
const STATIC_CACHE_NAME = 'bakasana-static-v1.2.0';
const DYNAMIC_CACHE_NAME = 'bakasana-dynamic-v1.2.0';
const IMAGE_CACHE_NAME = 'bakasana-images-v1.2.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/images/logo.png',
  '/images/logo-white.png',
  '/fonts/CormorantGaramond-Regular.woff2',
  '/fonts/Inter-Regular.woff2',
  '/_next/static/css/app.css',
  '/_next/static/js/app.js'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  '/api/retreats',
  '/api/destinations',
  '/api/testimonials',
  '/api/blog'
];

// Image optimization cache
const IMAGE_CACHE_PATTERNS = [
  '/images/',
  '/_next/image',
  '/uploads/'
];

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
};

// Cache durations
const CACHE_DURATIONS = {
  STATIC: 30 * 24 * 60 * 60 * 1000, // 30 days
  DYNAMIC: 7 * 24 * 60 * 60 * 1000, // 7 days
  IMAGES: 30 * 24 * 60 * 60 * 1000, // 30 days
  API: 60 * 60 * 1000 // 1 hour
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      caches.open(DYNAMIC_CACHE_NAME),
      caches.open(IMAGE_CACHE_NAME)
    ]).then(() => {
      console.log('Service Worker installed successfully');
      return self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE_NAME && 
              cacheName !== DYNAMIC_CACHE_NAME && 
              cacheName !== IMAGE_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker activated successfully');
      return self.clients.claim();
    })
  );
});

// Fetch event - handle requests with appropriate caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip Chrome extensions
  if (url.protocol === 'chrome-extension:') {
    return;
  }
  
  // Handle different request types
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else {
    event.respondWith(handleNavigationRequest(request));
  }
});

// Handle image requests with cache-first strategy
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    // Check if cache is still valid
    const cachedDate = new Date(cachedResponse.headers.get('date'));
    const isExpired = Date.now() - cachedDate.getTime() > CACHE_DURATIONS.IMAGES;
    
    if (!isExpired) {
      return cachedResponse;
    }
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Clone response before caching
      const responseClone = networkResponse.clone();
      await cache.put(request, responseClone);
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Image fetch failed:', error);
    return cachedResponse || new Response('Image not available', { status: 404 });
  }
}

// Handle API requests with network-first strategy
async function handleAPIRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Add timestamp header for cache validation
      const modifiedResponse = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: {
          ...networkResponse.headers,
          'cached-at': Date.now().toString()
        }
      });
      
      await cache.put(request, modifiedResponse.clone());
      return modifiedResponse;
    }
    
    throw new Error(`API request failed with status ${networkResponse.status}`);
  } catch (error) {
    console.error('API request failed:', error);
    
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      // Check if cached API response is still valid
      const cachedAt = parseInt(cachedResponse.headers.get('cached-at') || '0');
      const isExpired = Date.now() - cachedAt > CACHE_DURATIONS.API;
      
      if (!isExpired) {
        return cachedResponse;
      }
    }
    
    return new Response(JSON.stringify({ error: 'Service unavailable' }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Static asset fetch failed:', error);
    return new Response('Asset not available', { status: 404 });
  }
}

// Handle navigation requests with stale-while-revalidate strategy
async function handleNavigationRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  // Return cached response immediately if available
  if (cachedResponse) {
    // Update cache in background
    updateCacheInBackground(request, cache);
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Navigation request failed:', error);
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      const offlineResponse = await cache.match('/offline');
      if (offlineResponse) {
        return offlineResponse;
      }
    }
    
    return new Response('Page not available offline', { status: 404 });
  }
}

// Background cache update
async function updateCacheInBackground(request, cache) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
  } catch (error) {
    console.error('Background cache update failed:', error);
  }
}

// Helper functions
function isImageRequest(request) {
  return IMAGE_CACHE_PATTERNS.some(pattern => request.url.includes(pattern)) ||
         /\.(jpg|jpeg|png|gif|webp|svg|avif)$/i.test(request.url);
}

function isAPIRequest(request) {
  return API_CACHE_PATTERNS.some(pattern => request.url.includes(pattern)) ||
         request.url.includes('/api/');
}

function isStaticAsset(request) {
  return /\.(js|css|woff2|woff|ttf|eot)$/i.test(request.url) ||
         request.url.includes('/_next/static/');
}

// Background sync for form submissions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'booking-form') {
    event.waitUntil(syncBookingForm());
  } else if (event.tag === 'newsletter-signup') {
    event.waitUntil(syncNewsletterSignup());
  }
});

// Sync booking form data
async function syncBookingForm() {
  try {
    const bookingData = await getStoredBookingData();
    
    if (bookingData) {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bookingData)
      });
      
      if (response.ok) {
        await clearStoredBookingData();
        console.log('Booking form synced successfully');
      }
    }
  } catch (error) {
    console.error('Booking form sync failed:', error);
  }
}

// Sync newsletter signup
async function syncNewsletterSignup() {
  try {
    const newsletterData = await getStoredNewsletterData();
    
    if (newsletterData) {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newsletterData)
      });
      
      if (response.ok) {
        await clearStoredNewsletterData();
        console.log('Newsletter signup synced successfully');
      }
    }
  } catch (error) {
    console.error('Newsletter sync failed:', error);
  }
}

// IndexedDB helpers for offline data storage
async function getStoredBookingData() {
  // Implementation depends on your IndexedDB setup
  return null;
}

async function clearStoredBookingData() {
  // Implementation depends on your IndexedDB setup
}

async function getStoredNewsletterData() {
  // Implementation depends on your IndexedDB setup
  return null;
}

async function clearStoredNewsletterData() {
  // Implementation depends on your IndexedDB setup
}

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event);
  
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/images/logo-192x192.png',
      badge: '/images/badge-72x72.png',
      image: data.image,
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: data.primaryKey || 1,
        url: data.url || '/'
      },
      actions: [
        {
          action: 'explore',
          title: 'Explore',
          icon: '/images/icons/explore.png'
        },
        {
          action: 'close',
          title: 'Close',
          icon: '/images/icons/close.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow(event.notification.data.url)
    );
  }
});

// Periodic background sync for content updates
self.addEventListener('periodicsync', (event) => {
  console.log('Periodic sync triggered:', event.tag);
  
  if (event.tag === 'content-update') {
    event.waitUntil(updateContentCache());
  }
});

// Update content cache periodically
async function updateContentCache() {
  try {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    
    // Update key pages
    const keyPages = [
      '/',
      '/retreats',
      '/destinations',
      '/blog',
      '/about'
    ];
    
    for (const page of keyPages) {
      try {
        const response = await fetch(page);
        if (response.ok) {
          await cache.put(page, response.clone());
        }
      } catch (error) {
        console.error(`Failed to update cache for ${page}:`, error);
      }
    }
    
    console.log('Content cache updated successfully');
  } catch (error) {
    console.error('Content cache update failed:', error);
  }
}

// Clean up old cache entries
async function cleanupOldCacheEntries() {
  const caches = await caches.keys();
  
  for (const cacheName of caches) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      const responseDate = new Date(response.headers.get('date'));
      const age = Date.now() - responseDate.getTime();
      
      // Remove entries older than their cache duration
      let maxAge = CACHE_DURATIONS.DYNAMIC;
      if (cacheName === STATIC_CACHE_NAME) maxAge = CACHE_DURATIONS.STATIC;
      if (cacheName === IMAGE_CACHE_NAME) maxAge = CACHE_DURATIONS.IMAGES;
      
      if (age > maxAge) {
        await cache.delete(request);
      }
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldCacheEntries, 24 * 60 * 60 * 1000); // Daily cleanup